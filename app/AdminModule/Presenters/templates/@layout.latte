<!doctype html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="robots" content="noindex">

    <link rel="apple-touch-icon" sizes="180x180" href="{$basePath}/images/favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="{$basePath}/images/favicon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="{$basePath}/images/favicon/favicon-16x16.png">
    <link rel="manifest" href="{$basePath}/images/favicon/site.webmanifest">

    <title>{ifset title}{include title|striptags} | {/ifset}Kaufino ADMIN</title>

    {var $version = 0.06}

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">

    <!-- Core UI -->
    <link href="{$basePath}/coreui/dist/css/style.css" rel="stylesheet">

    <!-- Fonts CSS -->
    <link rel="stylesheet" href="{$basePath}/plugins/font-awesome/css/font-awesome.min.css">

    <!-- Datagrid CSS -->
    <link rel="stylesheet" href="{$basePath}/plugins/datagrid/datagrid-spinners.css">
    <link rel="stylesheet" href="{$basePath}/plugins/datagrid/datagrid.css">
    <!-- redactor.css -->
    <link rel="stylesheet" href="{$basePath}/libs/redactor/redactor.min.css" />
    <!-- jQuery JS -->
    <script src="{$basePath}/js/jquery-1.12.0.min.js"></script>

    <!-- Nette JS -->
    <script src="{$basePath}/js/netteForms.min.js"></script>
    <script src="{$basePath}/js/nette.ajax.js"></script>

    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

    <style>
        h1 {
            padding: 0 0 0 12px;
            font-size: 1.5rem;
        }
        .navbar {
            margin-bottom: 1rem;
        }

        .checkbox-wrapper {
            border: 1px solid #e5e7ea;
            padding: 10px 10px 0px 10px;
            background: #e5e7ea;;
        }

        a {
            color: #0a76bf
        }
    </style>
    <style>
        .toast {
            position: sticky;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }
    </style>

    {block head}{/block}
</head>
<body>

{foreach $flashes as $flash}
    {if $flash->type == 'success' || $flash->type == 'info'}
        <div class="toast-container position-fixed top-0 end-0 p-3">
            <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        Success! Changes was successfully saved.
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        </div>
    {/if}
{/foreach}

<nav class="navbar navbar-expand-lg navbar-light bg-light">
    <div class="container-fluid">
        <a class="navbar-brand" href="#">Kaufino</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent"
                aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                <li class="nav-item">
                    <a class="nav-link active" aria-current="page"  n:href=":Admin:Homepage:default">Countries</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" aria-current="page"  n:href=":Admin:Localization:default">Localizations</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" n:href=":Admin:Shop:default">Shops</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" n:href=":Admin:Offer:default">Offers</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" n:href=":Admin:Tag:default">Tags</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" n:href=":Admin:Seo:default">SEO</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" n:href=":Admin:Product:default">Products</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" n:href=":Admin:Coupon:default">Coupons</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" n:href=":Admin:LeafletPage:annotations">Annotate pages ({$headerCountOfleafletPagesRemaining})</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" n:href=":Admin:Offer:annotations">Annotate offers ({$headerCountOfOffersRemaining})</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" n:href=":Admin:Article:default">Articles</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" n:href=":Admin:Users:default">Users</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" n:href=":Admin:TrendingLeafletPages:default">Trending leaflet pages</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" n:href=":Admin:Cities:default">Cities</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" n:href=":Admin:Stores:default">Stores</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" n:href=":Admin:Translations:Translation:default">Translations</a>
                </li>
                {*
                <li class="nav-item">
                    <a class="nav-link" n:href=":Admin:Translations:Translation:default">Translations</a>
                </li>
                *}
            </ul>            
        </div>
    </div>
</nav>

{include content}

<!-- Optional JavaScript; choose one of the two! -->

<!-- Option 1: Bootstrap Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<!-- Select2 -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<!-- Option 2: Separate Popper and Bootstrap JS -->
<!--
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js" integrity="sha384-q2kxQ16AaE6UbzuKqyBE9/u/KzioAlnx2maXQHiDX9d4/zp8Ok3f+M7DPm+Ib6IU" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta1/dist/js/bootstrap.min.js" integrity="sha384-pQQkAEnwaBkjpqZ8RU1fF1AKtTcHJwFl3pblpTlHXybJjHpMYo79HY3hIi4NKxyj" crossorigin="anonymous"></script>
-->

<script>
    $(document).ready(function() {
        $('.multiselect').select2();
    });
</script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        let toast = document.getElementById('successToast');

        if (toast) {
            var successToast = new bootstrap.Toast(document.getElementById('successToast'));
            successToast.show();
            setTimeout(function () {
                successToast.hide();
            }, 5000);
        }
    });
</script>

<!-- Datagrid JS -->
<script src="{$basePath}/plugins/datagrid/datagrid-spinners.js"></script>
<script src="{$basePath}/plugins/datagrid/datagrid-instant-url-refresh.js"></script>
<script src="{$basePath}/plugins/datagrid/datagrid.js"></script>

{block scripts}{/block}
<!-- Redactor -->
<script src="{$basePath}/libs/redactor/redactor.min.js"></script>
<script src="{$basePath}/libs/redactor/langs/en.js"></script>
<script src="{$basePath}/libs/redactor/plugins/widget/widget.min.js"></script>
<script src="{$basePath}/libs/redactor/plugins/counter/counter.min.js"></script>
<script src="{$basePath}/libs/redactor/plugins/table/table.js"></script>
<script src="{$basePath}/js/redactor/redactor.js?v={$version}"></script>

</body>
</html>