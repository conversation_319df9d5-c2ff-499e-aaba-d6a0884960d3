@use "sass:color";
@use "tools";
@use "vars";

* {
  box-sizing: border-box;
}

*:before,
*:after {
  box-sizing: border-box;
}

html {
  font-size: 10px;
}

body {
  font-family: Nunito,Helvetica Neue,Helvetica,Arial,sans-serif;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  margin: 0;
  background-color: #fff;
  padding: 0px !important;  
}

p {
  margin: 0;
  line-height: 1.7;
}

a {
  color: #000;
  text-decoration: none;
}

.link {
  color: #2c2c2c;
  background-color: transparent;
  border: 0;
  cursor: pointer;
}

.link:hover {
  text-decoration: underline;
}

button.link {
  font-size: 14px;
  line-height: 1.25;
}

.container {
  position: relative;
  display: block;
  width: calc(100% - 30px);
  margin: auto;
  padding: 0 1rem;

  @include tools.breakpoint(vars.$desktop) {
    width: 1170px;
    padding: 0 3rem;
  }
}

.container--flex {
  display: flex;
  align-items: center;
  padding: 0 1rem;
}

.p-relative {
  position: relative;
}

.d-none {
  display: none;
}

.d-block {
  display: block;
}

.d-sm-block {
  @include tools.breakpoint(vars.$tablet) {
    display: block;
  }
}

.d-inline-block {
  display: inline-block;
}

.d-flex {
  display: flex;
}

.d-sm-flex {
  @include tools.breakpoint(vars.$tablet) {
    display: flex;
  }
}

.d-md-flex {
  @include tools.breakpoint(vars.$tablet--land) {
    display: flex;
  }
}

.align-items-center {
  align-items: center;
}


.flex-wrap {
  flex-wrap: wrap;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.flex-grow-1 {
  flex-grow: 1;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-sm-start {
  @include tools.breakpoint(vars.$tablet) {
    justify-content: flex-start;
  }
}

.flex-wrap-no-wrap {
  flex-wrap: nowrap;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-wrap-sm-no-wrap {
  @include tools.breakpoint(vars.$tablet) {
    flex-wrap: nowrap;
  }
}

.flex-direction-row {
  flex-direction: row;
}

.flex-direction-column {
  flex-direction: column;
}

.flex-direction-sm-row {
  @include tools.breakpoint(vars.$tablet) {
    flex-direction: row;
  }
}

.flex-direction-md-column {
  @include tools.breakpoint(vars.$tablet--land) {
    flex-direction: column;
  }
}

.flex-direction-lg-row {
  @include tools.breakpoint(vars.$min-lg) {
    flex-direction: row;
  }
}

.flex-direction-lg-column {
  @include tools.breakpoint(vars.$min-lg) {
    flex-direction: column;
  }
}

.w100 {
  width: 100%;
}

.h100 {
  height: 100%;
}

.w-sm-auto {
  @include tools.breakpoint(vars.$tablet) {
    width: auto;
  }
}

.pb-0 {
  padding-bottom: 0;
}

.pb-1 {
  padding-bottom: 0.25rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-3 {
  padding-bottom: 1rem;
}

.pb-4 {
  padding-bottom: 1.5rem;
}

.pb-5 {
  padding-bottom: 3rem;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.px-3 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-3 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.px-4 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.py-4 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.px-5 {
  padding-left: 3rem;
  padding-right: 3rem;
}

.py-5 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.px-lg-0 {
  @include tools.breakpoint(vars.$min-lg) {
    padding-left: 0rem;
    padding-right: 0rem;
  }
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-3 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.m-auto {
  margin: auto;
}

.mt-0 {
  margin-top: 0;
}

.mt-3 {
  margin-top: 1rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.mt-5 {
  margin-top: 3rem;
}

.mt-sm-0 {
  @include tools.breakpoint(vars.$tablet) {
    margin-top: 0rem;
  }
}

.mt-sm-5 {
  @include tools.breakpoint(vars.$tablet) {
    margin-top: 3rem;
  }
}

.mt-md-0 {
  @include tools.breakpoint(vars.$tablet--land) {
    margin-top: 0rem;
  }
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mb-5 {
  margin-bottom: 3rem;
}

.mb-6 {
  margin-bottom: 4.5rem;
}

.mb-7 {
  margin-bottom: 6rem;
}

.mb-sm-0 {
  @include tools.breakpoint(vars.$tablet) {
    margin-bottom: 0;
  }
}

.mb-sm-3 {
  @include tools.breakpoint(vars.$tablet) {
    margin-bottom: 1rem;
  }
}

.ml-auto {
  margin-left: auto;
}

.ml-sm-auto {
  @include tools.breakpoint(vars.$tablet) {
    margin-left: auto;
  }
}

.ml-md-auto {
  @include tools.breakpoint(vars.$tablet--land) {
    margin-left: auto;
  }
}

.ml-0 {
  margin-left: 0;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 1rem;
}

.ml-4 {
  margin-left: 1.5rem;
}

.ml-5 {
  margin-left: 3rem;
}

.mr-auto {
  margin-right: auto;
}

.mr-0 {
  margin-right: 0;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 1rem;
}

.mr-4 {
  margin-right: 1.5rem;
}

.mr-5 {
  margin-right: 3rem;
}

.mr-sm-2 {
  @include tools.breakpoint(vars.$tablet) {
    margin-right: 0.5rem;
  }
}

.ml-sm-3 {
  @include tools.breakpoint(vars.$tablet) {
    margin-left: 1rem;
  }
}

.mb-lg-0 {
  @include tools.breakpoint(vars.$min-lg) {
    margin-right: 0rem;
  }
}

.mb-lg-3 {
  @include tools.breakpoint(vars.$min-lg) {
    margin-right: 1rem;
  }
}

.mr-lg-3 {
  @include tools.breakpoint(vars.$min-lg) {
    margin-right: 1rem;
  }
}

.pl-sm-3 {
  @include tools.breakpoint(vars.$tablet) {
    padding-left: 1rem;
  }
}

.pl-sm-4 {
  @include tools.breakpoint(vars.$tablet) {
    padding-left: 1.5rem;
  }
}

.pr-sm-3 {
  @include tools.breakpoint(vars.$tablet) {
    padding-right: 1rem;
  }
}

.overflow-hidden {
  overflow: hidden;
}

.img-responsive {
  display: block;
  max-width: 100%;
  height: auto;
  margin: auto;
}

.ellipsis {
  @include tools.ellipsis(100%);
}

.fw-regular {
  font-weight: 400;
}

.fw-semibold {
  font-weight: 600;
}

.fw-bold,
.strong {
  font-weight: 700;
}

.tt-uppercase {
  text-transform: uppercase;
}

.td-underline {
  text-decoration: underline;
}

.td-none {
  text-decoration: none;
}

.td-hover-none:hover {
  text-decoration: none;
}

.td-hover-underline:hover {
  text-decoration: underline;
}

.ta-center {
  text-align: center;
}

.sm-ta-left {
  @include tools.breakpoint(vars.$tablet) {
  text-align: left;
  }
}

.mw-700 {
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.mw-700.ml-0 {
  margin-left: 0;
}

.min-w-300 {
  min-width: 300px;
}

.mw-400 {
  max-width: 400px;
}

.mw-805 {
  max-width: 805px;
}

small {
  font-size: inherit;
}

.fz-xxs {
  font-size: 11px;
}

.fz-s {
  font-size: 12px;
}

.fz-m {
  font-size: 14px;
}

.fz-l {
  font-size: 16px;
}

.fz-xl {
  font-size: 18px;
}

.fz-xxl {
  font-size: 20px;
}

.fz-sm-xs {
  @include tools.breakpoint(vars.$tablet) {
    font-size: 12px;
  }
}

.color-grey {
  color: vars.$color-grey;
}

.color-grey a {
  color: vars.$color-grey;
}

.color-light-grey {
  color: color.adjust(vars.$color-grey, $lightness: 40%);
}

.color-black {
  color: #000;
}

.lh-15 {
  line-height: 1.5;
}

.fw-700 {
  font-weight: 700;
}

.row {
  position: relative;
  display: flex;
  flex-wrap: wrap;
}

.col-6 {
  width: 50%;
  flex: 0 0 50%;
}

.col-12 {
  width: 100%;
  flex: 0 0 100%;
}

.col-sm-3 {
  @include tools.breakpoint(vars.$tablet) {
    width: 25%;
    flex: 0 0 25%;
  }
}

.col-sm-6 {
  @include tools.breakpoint(vars.$tablet) {
    width: 50%;
    flex: 0 0 50%;
  }
}

.col-sm-9 {
  @include tools.breakpoint(vars.$tablet) {
    width: 75%;
    flex: 0 0 75%;
  }
}

.col-md-6 {
  @include tools.breakpoint(vars.$desktop) {
    width: 50%;
    flex: 0 0 50%;
  }
}

.sm-column-count-2 {
  @include tools.breakpoint(vars.$tablet) {
    column-count: 2;
  }
}

.k-offers__item.hidden,
.k-shop__item.hidden,
.k-tag__inner.hidden,
.k-leaflets__item.hidden {
  display: none;
}

.k-show-more-button {
  font-size: 14px;
  padding: 1rem;
  border: 1px solid #b3b3b3;
  border-radius: 4px;    
  margin-right: auto;
  margin-left: auto;
}

.k-show-more-button.hidden {
  display: none;
}

.align-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.space-between {
  justify-content: space-between;
}

.underline {
  text-decoration: underline;
}

.text-center {
  text-align: center;
}

.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-grayscale);
}

//contest
.contest-form form {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.contest-form input {
  border: 1px solid #B3B3B3;
  padding: 1rem;
  border-radius: 10px;
  margin: 0.5rem 0;
  margin-right: 0.5rem;
  font-size: 16px;
  min-width: 270px;  
}

.contest-form input[type="submit"] {
  background-color: vars.$color-primary;
  color: #fff;
  font-weight: bold;
  min-width: 100px;
  cursor: pointer;
}

.contest-box {
  max-width: 720px;
  box-shadow: 0px 0px 8px 0px #00000026;
  margin: auto;
  padding: 1rem 2rem;
  margin-bottom: 3rem;
}

.contest-box h2 {
  font-size: 20px;
}

.contest-promo {  
  position: fixed;
  display: flex;
  align-items: center;
  width: 100%;
  bottom: 0px;
  background-color: vars.$color-primary;
  padding: 1rem 2rem;

  @include tools.breakpoint(vars.$tablet) {
    width: auto;
    bottom: 30px;
    right: 30px;
    border-radius: 20px;
    padding: 1.5rem 2rem;
    box-shadow: 0px 6px 20px 0px #00000066;
  }
}

.contest-promo__medal {
  width: 33px;
  height: 44px;
  margin-right: 1rem;
}

.contest-promo span {
  font-size: 18px;
  line-height: 22px;
  text-align: left;
  color: #fff;
  margin-right: 1rem;
}

.contest-promo strong {
  display: block;
  font-weight: 700;
}

.contest-promo__cross {
  margin-left: auto;
  cursor: pointer;
}

.hidden {
  display: none;
}

.kf-click-out {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity .15s;
  color: #fff;
}

.kf-click-out:hover {
  opacity: 1;
}

.kf-click-out:hover svg {
  cursor: pointer;
  -webkit-transform: scale(1.25);
  transform: scale(1.25);
}

.kf-click-out a {  
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kf-click-out svg {  
  background: vars.$color-primary;
  border-radius: 50%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  fill: #fff;  
  width: 1.75em;
  height: 1.75em;
  padding: .25em;
  pointer-events: none;
  -webkit-transition: -webkit-transform .3s cubic-bezier(.5,-.15,.7,4);
  transition: -webkit-transform .3s cubic-bezier(.5,-.15,.7,4);
  transition: transform .3s cubic-bezier(.5,-.15,.7,4);
  transition: transform .3s cubic-bezier(.5,-.15,.7,4), -webkit-transform .3s cubic-bezier(.5,-.15,.7,4);    
}

.montagu-slab {
  font-family: "Montagu Slab", serif;
  font-optical-sizing: auto;
  font-weight: 700;
  font-style: normal;
  color: vars.$color-primary;
}