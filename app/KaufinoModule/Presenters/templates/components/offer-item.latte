{var $offerLeaflet = $offer->getLeafletPage()->getLeaflet()}
<div class="k-offers__item {if $offer->isExpired()}expired{/if} {isset($cssClass) ? $cssClass}">
    <div class="k-offers__inner" data-line="{_kaufino.shop.expired}">
        {if $user->isLoggedIn()}
            <a n:href=":Admin:Offer:archive, $offer->getId(), $presenter->link('this')" style="position: absolute; top: 10px; right: 10px; background-color: #c30404; color: white; border-radius: 50%; padding: 3px 5px;">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" style="width: 20px; height: 20px">
                    <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                </svg>
            </a>            
        {/if}

        {if !$offer->isExpired()}
            {if isset($linkToTag) && $offer->getTags()->count() > 0}
                <a n:href="Offers:tag $offer->getTags()->last()" class="k-offers__image-wrapper">
            {else}
                <a href="{link Leaflet:leaflet shop => $offerLeaflet->getShop(), leaflet => $offerLeaflet}#p-{$offer->getLeafletPage()->getPageNumber()}" class="k-offers__image-wrapper">
             {/if}
        {else}
            <span class="k-offers__image-wrapper">
        {/if}

            <picture>
                <source 
                    type="image/webp"
                    srcset="
                        {$offer->getImageUrl()|image:150,150,'fit','webp'} 150w,
                        {$offer->getImageUrl()|image:300,300,'fit','webp'} 300w                            
                    "
                    sizes="150px"                     
                >            
                <img                    
                    src="{$offer->getImageUrl()|image:150,150,'fit'}"                             
                    srcset="
                        {$offer->getImageUrl()|image:150,150,'fit'} 150w,
                        {$offer->getImageUrl()|image:300,300,'fit'} 300w
                    "
                    sizes="150px"
                    width="150" 
                    height="150" 
                    alt="{$offer->getName()}" 
                    class="k-offers__image"                   
                    loading="lazy"
                    fetchpriority="auto"
                    decoding="async"
                >
            </picture>                 

        {if !$offer->isExpired()}
            </a>
        {else}
            </span>
        {/if}                        

        <div class="k-offers__content">
            <small n:if="!isset($hideShop)" class="k-offers__small k-offers__small--logo">                
                <a n:href="Shop:shop $offerLeaflet->getShop()" class="k-offers__logo-wrapper">                        
                    <img src="{$offer->getShop()->getLogoUrl() |image:80,80}" alt="{$offer->getShop()->getName()}" loading="lazy">
                </a>                
                {*if !isset($hideTags)}
                    {foreach $offer->getTags() as $tag}
                        {if !isset($hideShop) && !isset($hideTags)}|{/if} <a n:href="Tag:tag $tag">
                            {$tag->getName()}
                        </a>
                        {breakIf $iterator->getCounter() > 1}
                    {/foreach}
                {/if*}
            </small>
            <p class="k-offers__title line-clamp-2">            
                {if !$offer->isExpired()}    
                    <a href="{link Leaflet:leaflet shop => $offerLeaflet->getShop(), leaflet => $offerLeaflet}#p-{$offer->getLeafletPage()->getPageNumber()}">{$offer->getName()}</a>
                {else}
                    {$offer->getName()}
                {/if}
            </p>
            {*<p class="k-offers__text">{$offer->getDescription()}</p>*}
            <div class="k-offers__price">
                {*                
                {if !$offer->isExpired()}
                    <a href="{link Leaflet:leaflet shop => $offerLeaflet->getShop(), leaflet => $offerLeaflet}#p-{$offer->getLeafletPage()->getPageNumber()}" class="k-offers__link">{_kaufino.shop.link, [brand => $offer->getShop()->getName(), page => $offer->getLeafletPage()->getPageNumber()]|noescape}</a>
                {else}
                    <span class="k-offers__link">{_kaufino.shop.link, [brand => $offer->getShop()->getName(), page => $offer->getLeafletPage()->getPageNumber()]|noescape}</span>
                {/if}
                *}

                <div class="k-offers__right">                    
                    <div n:if="$offer->getCommonPrice() > 0">
                        <small>{$offer->getCommonPrice()|price:$offer->getLocalization()}</small>
                        <span class="k-offers__badge">-{$offer->getPercentageDiscount()}%</span>
                    </div>

                    <strong>{$offer->getCurrentPrice()|price:$offer->getLocalization()}</strong>                    

                    {if $offer->isFutureOffer()}
                        {var $color = $offer->getValidSinceDays() <= 2 ? 'green' : ''}

                        <span class="k-offers__tag {$color |noescape}">
                            {if $offer->getValidSinceDays() <= 6}
                                {_kaufino.offer.offerItem.validSinceDays, [count => $offer->getValidSinceDays()]}
                            {else}
                                {_kaufino.offer.offerItem.validSinceWeek}
                            {/if}
                        </span>
                    {else}
                        {var $color = $offer->getValidTillDays() === 0 ? 'red' : ($offer->getValidTillDays() <= 2 ? 'orange' : '')}

                        <span class="k-offers__tag {$color |noescape}">
                            {if $offer->getValidTillDays() <= 0}
                                {_kaufino.offer.offerItem.validTillToday}
                            {elseif $offer->getValidTillDays() <= 6}
                                {_kaufino.offer.offerItem.validTillDays, [count => $offer->getValidTillDays()]}
                            {else}
                                {_kaufino.offer.offerItem.validTillWeek}
                            {/if}
                        </span>
                    {/if}
                </div>
            </div>
        </div>
    </div>
</div>