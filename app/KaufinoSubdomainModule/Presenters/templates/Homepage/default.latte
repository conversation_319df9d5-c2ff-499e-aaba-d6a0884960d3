{capture $shopsInText}{var $items = ($shops|slice: 0, 8)}{foreach $items as $_shop}{last}{if count($items) > 1} {_kaufino.homepage.and} {/if}{/last}<a n:href="Shop:shop $_shop">{$_shop->getName()}</a>{sep}{if $iterator->getCounter() < count($items)-1}, {/if}{/sep}{/foreach}{/capture}
{capture $shopsInTextMetaDesc}{var $items = ($shops|slice: 0, 8)}{foreach $items as $_shop}{last}{if count($items) > 1} {_kaufino.homepage.and} {/if}{/last}{$_shop->getName()}{sep}{if $iterator->getCounter() < count($items)-1}, {/if}{/sep}{/foreach}{/capture}


{block scripts}
    {include parent}    

    <script defer src="{$basePath}/js/swiper/swiper-bundle.min.js"></script>
    <script defer src="{$basePath}/js/swiper.js"></script>
{/block}

{block hreflang}
    {var $otherWebsites = $footerWebsites()}

    {foreach $otherWebsites as $otherWebsite}
        {var $localizedUrl = $getLocalizedUrl($otherWebsite)}
        <link rel="alternate" n:attr="hreflang: $otherWebsite !== $website ? $otherWebsite->getLocalization()->getFullLocale('-') : 'x-default'" href={$localizedUrl} />
    {/foreach}
{/block}

{block title}{_kaufino.homepage.metaTitle}{/block}
{block description}{_kaufino.homepage.metaDescription, [shopsInText => $shopsInTextMetaDesc]}{/block}

{block head}
    {include parent}
    <script n:syntax="double">        
        window.dataLayer.push({
            'content_group' : 'Homepage',
            'country' : {{$localization->getRegion()}}
        });
    </script>

    {if ($channel === 'l')}
        <script>
        // Definování Out-of-page slotů (sticky a interstitial)
        googletag.cmd.push(function() {
            googletag.defineOutOfPageSlot('/23037269705/kaufino-com_top/kaufino-com_popup_testgroup1B', 'sticky').addService(googletag.pubads());
            googletag.defineOutOfPageSlot('/23037269705/kaufino-com_top/kaufino-com_interstitial_testgroup1B', 'interstitial').addService(googletag.pubads());
            googletag.enableServices();
        });
        </script>
    {elseif ($channel === 'm')}
        <script async src="https://cdn.performax.cz/yi/adsbypx/px_autoads.js?aab=ulite"></script>
        <link rel="stylesheet" href="https://cdn.performax.cz/yi/adsbypx/px_autoads.css"/>        
    {elseif ($channel === 'n')}
        <script async src="https://cdn.performax.cz/yi/adsbypx/px_autoads.js?aab=ulite"></script>
        <link rel="stylesheet" href="https://cdn.performax.cz/yi/adsbypx/px_autoads.css"/>
    {/if}  
{/block}

{block content}

{if ($channel === 'l')}
    <!-- Slot pro Sticky formáty -->
    <div id='sticky' style="height:0">
        <script>
            googletag.cmd.push(function() { googletag.display('sticky'); });
        </script>
    </div>

    <!-- Block pro formát Interstitial -->
    <div id='interstitial' style="height:0">
        <script>
            googletag.cmd.push(function() { googletag.display('interstitial'); });
        </script>
    </div>
{/if}

{include adUnit, 'homepage_ad_1', 'desktop'}
{include adUnit, 'homepage_ad_1_mob', 'mobile'}

<div class="container">
    <div class="swiper k-hp-swiper">
        <div class="swiper-wrapper">
            {foreach $shops as $shop}
                <a n:href="Shop:shop $shop" title="{_kaufino.shop.storeLeaflet, [brand => $shop->getName()]}" class="swiper-slide">
                    <picture class="img-wrapper">                        
                        <source 
                            srcset="
                                {$shop->getLogoUrl() |image:80,70,'fit','webp'} 1x,
                                {$shop->getLogoUrl() |image:160,140,'fit','webp'} 2x
                            " 
                            type="image/webp"
                        >                                    
                        <img 
                            src="{$basePath}/images/placeholder-80x70.png" 
                            srcset="
                                {$shop->getLogoUrl() |image:80,70,'fit','png'} 1x,
                                {$shop->getLogoUrl() |image:160,140,'fit','png'} 2x
                            "
                            data-sizes="auto"
                            width="80" 
                            height="70" 
                            alt="{$shop->getName()}" 
                            class=""
                            loading="lazy"
                        >
                    </picture>   
                    
                </a>
            {/foreach}
        </div>
        <div class="swiper-pagination"></div>
        <div class="swiper-button-prev"></div>
        <div class="swiper-button-next"></div>
    </div>

    {*
    <div class="k-shop">
        {foreach $shops as $shop}
            {include '../components/shop-logo.latte', shop => $shop}
        {/foreach}
    </div>    
    *}

    <h1 class="k__title mt-4 mt-sm-5 mb-4">        
        {_kaufino.homepage.leaflets}
    </h1>

    <div class="k-leaflets__wrapper  k-leaflets__wrapper--xs-mx">
        {foreach $leaflets as $leaflet}
            {include '../components/leaflet.latte', leaflet => $leaflet, validBadgeShow => false}
        {/foreach}
    </div>

    <div class="d-flex mt-3">
        <a href="{link Leaflets:leaflets}" class="link ml-auto k-show-more-button">
            {_kaufino.homepage.allLeaflets} »
        </a>
    </div>

    {include adUnit, 'homepage_ad_2', 'desktop'}
    {include adUnit, 'homepage_ad_2_mob', 'mobile'}

    {if ($channel === 'n')}
        <div class="ads-container">
            <div class="ads-label">{_'kaufino.leaflet.ads'}</div>    
            <!-- letaky.kaufino.com / billboard1-direct -->
            <script src="https://cdn.performax.cz/px2/flexo.js?1" async></script>
            <div id="PX_35670_882042459640355"></div>
            <script>
                window.px2 = window.px2 || { conf: {},queue: [] };
                px2.queue.push(function () {
                    px2.render({
                        slot: {
                            id: 35670
                        },
                        elem: "PX_35670_882042459640355"
                    })
                });
            </script>      

            <!-- letaky.kaufino.com / mobile-rectangle1-direct -->      
            <div id="PX_35685_778363952239437"></div>
            <script>
                window.px2 = window.px2 || { conf: {},queue: [] };
                px2.queue.push(function () {
                    px2.render({
                        slot: {
                            id: 35685
                        },
                        elem: "PX_35685_778363952239437"
                    })
                });
            </script>
        </div>      
    {/if}  

    <div n:if="count($cities) > 0">
        <h2 class="k__title">
            {_kaufino.homepage.city}
        </h2>             

        <div class="k-tag mb-3">
            {foreach $cities as $city}
                <div class="k-tag__inner">
                    <a n:href="City:city $city" class="k-tag__item">{$city->getName()}</a>
                </div>
            {/foreach}            
        </div>

        <div class="d-flex mt-3 mb-5">            
            <a n:href="Cities:cities" class="link ml-auto k-show-more-button">
                {_'kaufino.showMore.cities'} »
            </a>
        </div>
    </div>

    {include adUnit, 'homepage_ad_3', 'desktop'}
    {include adUnit, 'homepage_ad_3_mob', 'mobile'}

    {if $channel === 'n'}
        <div class="ads-container">
            <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
            <!-- letaky.kaufino.com / billboard2-direct -->
            <script src="https://cdn.performax.cz/px2/flexo.js?1" async></script>
            <div id="PX_35664_194003039996025"></div>
            <script>
                window.px2 = window.px2 || { conf: {},queue: [] };
                px2.queue.push(function () {
                    px2.render({
                        slot: {
                            id: 35664
                        },
                        elem: "PX_35664_194003039996025"
                    })
                });
            </script>

            <!-- letaky.kaufino.com / mobile-rectangle2-direct -->
            <script src="https://cdn.performax.cz/px2/flexo.js?1" async></script>
            <div id="PX_35667_493690343150761"></div>
            <script>
                window.px2 = window.px2 || { conf: {},queue: [] };
                px2.queue.push(function () {
                    px2.render({
                        slot: {
                            id: 35667
                        },
                        elem: "PX_35667_493690343150761"
                    })
                });
            </script>
        </div>      
    {/if}
    
    <div class="k-content">            
        <h2>{_kaufino.homepage.bottomText.mainTitle}</h2>
        
        <h3>{_kaufino.homepage.bottomText.section1.title}</h3>
        <p>{_kaufino.homepage.bottomText.section1.text|noescape}</p>

        <h3>{_kaufino.homepage.bottomText.section2.title}</h3>
        <p>{_kaufino.homepage.bottomText.section2.text|noescape}</p>

        <h3>{_kaufino.homepage.bottomText.section3.title}</h3>
        <p>{_kaufino.homepage.bottomText.section3.text|noescape}</p>
        
        <h3>{_kaufino.homepage.bottomText.section4.title}</h3>
        <p>{_kaufino.homepage.bottomText.section4.text|noescape}</p>
        <p>{_kaufino.homepage.bottomText.section4.text2|noescape}</p>
    </div>    

    {include adUnit, 'homepage_ad_4', 'desktop'}
    {include adUnit, 'homepage_ad_4_mob', 'mobile'}    
</div>
