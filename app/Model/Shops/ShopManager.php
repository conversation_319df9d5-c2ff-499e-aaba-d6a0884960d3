<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Shops;

use <PERSON><PERSON><PERSON>\Model\EntityManager;
use Ka<PERSON>ino\Model\Localization\Entities\Localization;
use <PERSON><PERSON>ino\Model\ResponseCacheManager;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Sentry\Response;

class ShopManager
{
	/** @var EntityManager */
	private $em;

	/** @var ResponseCacheManager */
	private $responseCacheManager;

	public function __construct(EntityManager $em, ResponseCacheManager $responseCacheManager)
	{
		$this->em = $em;
		$this->responseCacheManager = $responseCacheManager;
	}

	public function createShop(Localization $localization, string $name, string $slug): Shop
	{
		$shop = new Shop($localization, $name, $slug);

		return $this->saveShop($shop);
	}

	public function saveShop(Shop $shop, bool $resetCache = true): Shop
	{
		$this->em->persist($shop);
		$this->em->flush();

		if ($resetCache === true) {
			$this->responseCacheManager->clearCacheByTag('shops');
			$this->responseCacheManager->clearCacheByTag('shop/' . $shop->getId());
		}

		return $shop;
	}
}
