!function(a){a.add("plugin","properties",{modals:{properties:'<form action="">                     <div class="form-item">                         <label>Id</label>                         <input type="text" name="id">                     </div>                     <div class="form-item">                         <label>Class</label>                         <input type="text" name="classname">                     </div>                 </form>'},translations:{en:{properties:"Properties"}},init:function(t){this.app=t,this.opts=t.opts,this.lang=t.lang,this.$body=t.$body,this.toolbar=t.toolbar,this.inspector=t.inspector,this.selection=t.selection,this.labelStyle={"font-family":"monospace",position:"absolute",padding:"2px 5px","line-height":1,"border-radius":"3px","font-size":"11px",color:"rgba(255, 255, 255, .9)"}},onmodal:{properties:{open:function(t,e){if(this.$block){var i=this._getData(this.$block);e.setData(i)}},opened:function(t,e){e.getField("id").focus()},save:function(t,e){var i=e.getData();this._save(i)}}},onbutton:{properties:{observe:function(t){this._observeButton(t)}}},onclick:function(){},start:function(){var t={title:this.lang.get("properties"),api:"plugin.properties.open",observe:"properties"};this.toolbar.addButton("properties",t).setIcon('<i class="re-icon-properties"></i>'),this._createLabel()},stop:function(){this._removeLabel()},open:function(){var t=this.selection.getBlock();if(t){this.$block=a.dom(t);var e={title:this.lang.get("properties"),width:"500px",name:"properties",handle:"save",commands:{save:{title:this.lang.get("save")},cancel:{title:this.lang.get("cancel")}}};this.app.api("module.modal.build",e)}},_save:function(t){this.app.api("module.modal.close"),""===t.id?this.$block.removeAttr("id"):this.$block.attr("id",t.id),""===t.classname?this.$block.removeAttr("class"):this.$block.attr("class",t.classname)},_getData:function(t){var e=a.dom(t);return{id:e.attr("id"),classname:e.attr("class")}},_showData:function(t,e){var i="";if(e.id&&(i+="#"+e.id+" "),e.classname&&(i+="."+e.classname),""!==i){var s=a.dom(t).offset();this.$label.css({top:s.top-12+"px",left:s.left+"px","z-index":this.opts.zindex?this.opts.zindex+3:"auto"}),this.$label.html(i),this.$label.show()}else this.$label.hide()},_createLabel:function(){this.$label=a.dom("<span />"),this.$label.hide(),this.$label.css(this.labelStyle).css("background","rgba(229, 57, 143, .7)"),this.$body.append(this.$label)},_removeLabel:function(){this.$label&&this.$label.remove()},_observeButton:function(t){var e=this.selection.getBlock(),i=this.inspector.parse(e);if(e&&!i.isComponent()){var s=this._getData(e);this._showData(e,s),t.enable()}else t.disable(),this.$label&&this.$label.hide()}})}(Redactor);