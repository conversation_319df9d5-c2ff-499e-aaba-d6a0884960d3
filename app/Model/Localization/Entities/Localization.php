<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Localization\Entities;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Localization\Repositories\LocalizationRepository")
 * @ORM\Table(name="kaufino_localization_localization")
 */
class Localization
{
	public const LOCALE_CZECH = 'cs';
	public const LOCALE_SLOVAK = 'sk';
	public const LOCALE_POLISH = 'pl';
	public const LOCALE_ROMANIAN = 'ro';
	public const LOCALE_ENGLISH = 'en';
	public const LOCALE_HUNGARIAN = 'hu';
	public const LOCALE_CROATIAN = 'hr';
	public const LOCALE_ITALY = 'it';
	public const LOCALE_DENMARK = 'da';
	public const LOCALE_NETHERLANDS = 'nl';
	public const LOCALE_FRANCE = 'fr';
	public const LOCALE_LATVIAN = 'lv';
	public const LOCALE_LITHUANIAN = 'lt';	

	public const REGION_CZECHIA = 'cz';
	public const REGION_SLOVAKIA = 'sk';
	public const REGION_POLAND = 'pl';
	public const REGION_HUNGARY = 'hu';
	public const REGION_ROMANIAN = 'ro';
	public const REGION_UNITED_KINGDOM = 'en';
	public const REGION_UNITED_STATES = 'us';
	public const REGION_CANADA = 'ca';
	public const REGION_DENMARK = 'dk';
	public const REGION_FRANCE = 'fr';
	public const REGION_NETHERLANDS = 'nl';
	public const REGION_GERMANY = 'de';
	public const REGION_BELGIAN = 'be';
	public const REGION_GREECE = 'gr';
	public const REGION_JAR = 'za';	
	public const REGION_FINLAND = 'fi';
	public const REGION_SWEDISH = 'se';
	public const REGION_AUSTRIAN = 'at';
	public const REGION_SWITZERLAND = 'ch';
	public const REGION_NORWAY = 'no';
	public const REGION_PORTUGUESE = 'pt';
	public const REGION_SPAIN = 'es';
	public const REGION_GREATBRITAIN = 'gb';
	public const REGION_BULGARIAN = 'bg';
	public const REGION_BRAZILIAN = 'br';
	public const REGION_SERBIAN = 'rs';
	public const REGION_SLOVENIAN = 'si';
	public const REGION_ESTONIAN = 'ee';
	public const REGION_CYPRIAN = 'cy';
	public const REGION_MOLDAVIAN = 'md';
	public const REGION_TURKIAN = 'tr';
	public const REGION_AUSTRALIAN = 'au';

    public const LOCALE_AND_REGION_CZECHIA = self::LOCALE_CZECH . '_CZ';

	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=false)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\Column(type="string", length=2, nullable=false, options={"fixed" = true})
	 */
	protected $region;

	/**
	 * @ORM\Column(type="string", length=2, nullable=false, options={"fixed" = true})
	 */
	protected $locale;

	/**
	 * @ORM\Column(type="string", length=5, nullable=false, options={"fixed" = true})
	 */
	protected $fullLocale;

	/**
	 * @ORM\Column(type="string", length=64, nullable=false)
	 */
	protected $name;

    /**
     * @ORM\Column(type="string", length=64, nullable=true)
     */
    private ?string $longDateFormat = null;

    /**
     * @ORM\Column(type="string", length=64, nullable=true)
     */
    private ?string $shortDateFormat = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private ?\DateTime $leafletsSynchronizationStartedAt = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private ?\DateTime $leafletsSynchronizedAt = null;

	/**
	 * @ORM\Column(type="string", length=64, nullable=false)
	 */
	protected $original_name;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $active = true;

	public function getId(): int
	{
		return $this->id;
	}

	public function getLocale(): string
	{
		return $this->locale;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function isCzech(): bool
	{
		return $this->locale === self::LOCALE_CZECH;
	}

	public function isSlovak(): bool
	{
		return $this->locale === self::LOCALE_SLOVAK;
	}

	public function isPolish(): bool
	{
		return $this->locale === self::LOCALE_POLISH;
	}

	public function isRomanian(): bool
	{
		return $this->region === self::REGION_ROMANIAN;
	}

	public function isEnglish(): bool
	{
		return $this->locale === self::LOCALE_ENGLISH;
	}

	public function isHungarian(): bool
	{
		return $this->locale === self::LOCALE_HUNGARIAN;
	}

	public function isItaly(): bool
	{
		return $this->locale === self::LOCALE_ITALY;
	}

	public function isCroatian(): bool
	{
		return $this->locale === self::LOCALE_CROATIAN;
	}

	public function isCanadian(): bool
	{
		return $this->region === self::REGION_CANADA;
	}

	public function isFrancian(): bool
	{
		return $this->region === self::REGION_FRANCE;
	}

	public function isNetherlandian(): bool
	{
		return $this->region === self::REGION_NETHERLANDS;
	}

	public function isDenmarkian(): bool
	{
		return $this->region === self::REGION_DENMARK;
	}	

	public function isBelgian(): bool
	{
		return $this->region === self::REGION_BELGIAN;
	}

	public function isGermany(): bool
	{
		return $this->region === self::REGION_GERMANY;
	}

	public function isGreecian(): bool
	{
		return $this->region === self::REGION_GREECE;
	}

	public function isJar(): bool
	{
		return $this->region === self::REGION_JAR;
	}

	public function isFinlandian(): bool
	{
		return $this->region === self::REGION_FINLAND;
	}

	public function isSwedian(): bool
	{
		return $this->region === self::REGION_SWEDISH;
	}

	public function isNorwaian(): bool
	{
		return $this->region === self::REGION_NORWAY;
	}

	public function isAustrian(): bool
	{
		return $this->region === self::REGION_AUSTRIAN;
	}

	public function isSwitzerlandian(): bool
	{
		return $this->region === self::REGION_SWITZERLAND;
	}
	
	public function isPortuguesian(): bool
	{
		return $this->region === self::REGION_PORTUGUESE;
	}
	
	public function isSpaian(): bool
	{
		return $this->region === self::REGION_SPAIN;
	}

	public function isGreatbritian(): bool
	{
		return $this->region === self::REGION_GREATBRITAIN;
	}
	
	public function isLatvian(): bool
	{
		return $this->region === self::LOCALE_LATVIAN;
	}

	public function isSlovenian(): bool
	{
		return $this->region === self::REGION_SLOVENIAN;
	}

	public function isSerbian(): bool
	{
		return $this->region === self::REGION_SERBIAN;
	}

	public function isLithuanian(): bool
	{
		return $this->region === self::LOCALE_LITHUANIAN;
	}

	public function isBulgarian(): bool
	{
		return $this->region === self::REGION_BULGARIAN;
	}

	public function isEstonian(): bool
	{
		return $this->region === self::REGION_ESTONIAN;
	}

	public function isBrazilian(): bool
	{
		return $this->region === self::REGION_BRAZILIAN;
	}

	public function isCyprian(): bool
	{
		return $this->region === self::REGION_CYPRIAN;
	}

	public function isMoldavian(): bool
	{
		return $this->region === self::REGION_MOLDAVIAN;
	}

	public function isTurkian(): bool
	{
		return $this->region === self::REGION_TURKIAN;
	}

	public function isAustralian(): bool
	{
		return $this->region === self::REGION_AUSTRALIAN;
	}

	public function isUnitedStatesAmerican(): bool
	{
		return $this->region === self::REGION_UNITED_STATES;
	}

	/**
	 * @return string
	 */
	public function getRegion(): string
	{
		return $this->region;
	}

	/**
	 * @return string
	 */
	public function getOriginalName(): string
	{
		return $this->original_name;
	}

	/**
	 * @return bool
	 */
	public function isActive(): bool
	{
		return $this->active;
	}

	/**
	 * @return string
	 */
	public function getFullLocale(string $delimiter = '_'): string
	{
		return $delimiter !== '_' ? str_replace('_', $delimiter, $this->fullLocale) : $this->fullLocale;
	}

	public function hasArticles(): bool
	{
		return in_array($this->getLocale(), [
			self::LOCALE_CZECH,
			self::LOCALE_SLOVAK,
			self::LOCALE_ROMANIAN,
			self::LOCALE_HUNGARIAN,
			self::LOCALE_ITALY,
		]);
	}

	public function isGeneratedMetaDataAllowed(): bool
	{
		return in_array($this->getLocale(), [
			self::LOCALE_CZECH,
			self::LOCALE_SLOVAK,
			self::LOCALE_ROMANIAN,
			self::LOCALE_ITALY,
			self::LOCALE_CROATIAN,
			self::LOCALE_NETHERLANDS,
			self::LOCALE_POLISH,
			self::LOCALE_FRANCE,
			self::LOCALE_HUNGARIAN,
		]);
	}

    public function finishLeafletSynchronization()
    {
        $this->leafletsSynchronizedAt = new \DateTime();
    }

    public function startLeafletSynchronization()
    {
        $this->leafletsSynchronizationStartedAt = new \DateTime();
        $this->leafletsSynchronizedAt = null;
    }

    public function hasGeolocation(): bool
    {
        return $this->isItaly() || $this->isRomanian() || $this->isFrancian() || $this->isCzech() || $this->isPolish() || $this->isHungarian() || $this->isGermany() || $this->isSlovak();
    }

    public function getLongDateFormat(): ?string
    {
        return $this->longDateFormat;
    }

    public function getShortDateFormat(): ?string
    {
        return $this->shortDateFormat;
    }

    public function setLongDateFormat(?string $longDateFormat): void
    {
        $this->longDateFormat = $longDateFormat;
    }

    public function setShortDateFormat(?string $shortDateFormat): void
    {
        $this->shortDateFormat = $shortDateFormat;
    }

    public function hasOffers(): bool
    {
        $localesWithOffers = ['cs', 'de', 'hu', 'it', 'nl', 'pl', 'ro', 'rs', 'sk', 'za'];

        return in_array($this->getLocale(), $localesWithOffers);
    }
}
