<div class="k-leaflets__item mb-5">
    {var $leaflet = $leafletPage->getLeaflet()}
    {if isset($tagId) && $user->isLoggedIn()}
        <a n:href=":Admin:LeafletPage:excludeFromTag $leafletPage->getId(), $tag->getId(), $presenter->link('this')" style="position: absolute; z-index:999; top: 10px; right: 10px; background-color: #c30404; color: white; border-radius: 50%; padding: 3px 5px;">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" style="width: 20px; height: 20px">
                <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
            </svg>
        </a>
    {/if}

    {if $user->isLoggedIn()}
        <a n:href=":Admin:LeafletPage:annotations, $leafletPage->getId()" target="_blank" style="position: absolute; z-index:999; top: 10px; right: 45px; background-color: #099e4a; color: white; border-radius: 50%; padding: 3px 5px;">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="width: 20px; height: 20px">
                <path stroke-linecap="round" stroke-linejoin="round" d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10" />
            </svg>
        </a>
    {/if}

    <a href="{link Leaflet:leaflet shop => $leaflet->getShop(), leaflet => $leaflet}#p-{$leafletPage->getPageNumber()}" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} mb-3">
        <picture>
            <source 
                type="image/webp"
                srcset="
                    {$leafletPage->getImageUrl() |image:165,203,'exactTop','webp'} 165w,
                    {$leafletPage->getImageUrl() |image:230,288,'exactTop','webp'} 230w,
                    {$leafletPage->getImageUrl() |image:460,576,'exactTop','webp'} 460w
                "                 
                sizes="(max-width: 504px) 165px, 230px"
            >            
            <img 
                src="{$leafletPage->getImageUrl() |image:165,203,'exactTop'}"                             
                srcset="
                    {$leafletPage->getImageUrl() |image:165,203,'exactTop'} 165w,
                    {$leafletPage->getImageUrl() |image:230,288,'exactTop'} 230w,
                    {$leafletPage->getImageUrl() |image:460,576,'exactTop'} 460w
                "
                data-sizes="auto"
                width="230" 
                height="288" 
                alt="{$leaflet->getName()}" 
                class="k-leaflets__image"
                loading="lazy"
                fetchpriority="auto"
                decoding="async"
            >
        </picture>                    
    </a>
    <div class="k-leaflets__title mt-0 mb-0">
        <a href="{link Leaflet:leaflet shop => $leaflet->getShop(), leaflet => $leaflet}#p-{$leafletPage->getPageNumber()}" class="color-black">
            {$leaflet->getName()}
        </a>
    </div>                
    <p class="k-leaflets__date mt-0 mb-0">{if $localization->isHungarian()}{$leaflet->getValidSince()|localDate:'long'} – {$leaflet->getValidTill()|localDate}{else}{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}{/if}</p>
</div>  