<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Forms;

use <PERSON><PERSON><PERSON>\Model\Images\ImageStorage;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\Tags\Entities\Tag;
use <PERSON><PERSON><PERSON>\Model\Tags\TagFacade;
use <PERSON><PERSON><PERSON>\Model\Tools;
use Ka<PERSON>ino\Model\Users\UserIdentity;
use Nette;
use Nette\Application\UI\Form;
use Nette\InvalidArgumentException;
use Nette\Utils\Strings;
use Kaufino\Model\Products\Entities\Product;
use Kaufino\Model\Products\ProductFacade;

class ProductControl extends Nette\Application\UI\Control
{
	/** @var array */
	public $onSuccess = [];

	/** @var ?Product */
	private $product;

	/** @var ProductFacade */
	private $productFacade;

	/** @var ImageStorage */
	private $imageStorage;

	/** @var LocalizationFacade */
	private $localizationFacade;

	/** @var TagFacade */
	private $tagFacade;

	/**
	 * @var UserIdentity
	 */
	private $userIdentity;

	public function __construct(Product $product = null, ProductFacade $productFacade, ImageStorage $imageStorage, LocalizationFacade $localizationFacade, TagFacade $tagFacade, UserIdentity $userIdentity)
	{
		$this->product = $product;
		$this->productFacade = $productFacade;
		$this->imageStorage = $imageStorage;
		$this->localizationFacade = $localizationFacade;
		$this->tagFacade = $tagFacade;
		$this->userIdentity = $userIdentity;
	}

	/**
	 * @return Form
	 */
	public function createComponentForm()
	{
		$form = new Form();

		$form->addSelect('localization', 'Localization:', $this->localizationFacade->findPairs())
			->setPrompt('-- select a localization --')
			->setRequired('Select a localization.');

		$form->addText('name', 'Name:')
			->setRequired('Enter a name.');

		if ($this->product) {
			$form->addText('slug', 'Slug:')
				->setDisabled();
		}

		if ($this->product) {
			$form->addMultiSelect('tags', 'Tags:', $this->tagFacade->findPairs($this->product->getLocalization(), Tag::TYPE_OFFERS))
				->setRequired('Select a tag.');
		}

		$form->addUpload('image', 'Image (Preferably PNG):')
			->addCondition(Form::FILLED)
			->addRule(Form::IMAGE, 'Image must be in JPEG, PNG or GIF');

		$form->addText('brand', 'Brand:');

		$form->addText('manufacturer', 'Manufacturer:');

		$form->addTextArea('description', 'Description:', null, 10)
			->setHtmlAttribute('class', 'redactor')
			->setRequired(false);

		$form->addInteger('priority', 'Priority:')
			->setDefaultValue(0)
			->setRequired(false);

		$form->addSubmit('submit', 'Save');
		$form->addSubmit('submitAndContinue', 'Save and continue editing');

		if ($this->product) {
			$form->setDefaults(
				[
					'localization' => $this->product->getLocalization()->getId(),
					'name' => $this->product->getName(),
					'slug' => $this->product->getSlug(),
					'brand' => $this->product->getBrand(),
					'manufacturer' => $this->product->getManufacturer(),
					'description' => $this->product->getDescription(),
					'priority' => $this->product->getPriority(),
					//'tag' => $this->product->getTag() ? $this->product->getTag()->getId() : null
				]
			);
		}

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			if ($this->product) {
				$product = $this->product;
				$product->setName($values->name);

				$product->clearTags();
				foreach ($values->tags as $tagId) {
					$tag = $this->tagFacade->findTag($tagId);
					$product->addTag($tag);
				}
			} else {
				$localization = isset($values->localization) ? $this->localizationFacade->findLocalization($values->localization) : null;
				$slug = Strings::webalize($values->name);

				if (isset($values->slug) && $this->productFacade->findProductBySlug($localization, $slug)) {
					throw new InvalidArgumentException('This slug is already used at another product.');
				}

				$product = $this->productFacade->createProduct($localization, $values->name, $slug);
			}

			$product->setDescription($values->description);
			$product->setBrand($values->brand);
			$product->setManufacturer($values->manufacturer);
			$product->setPriority($values->priority);

			$this->productFacade->saveProduct($product);

			/** @var Nette\Http\FileUpload $image */
			$image = $values->image;

			if ($image->isOk() && $image->isImage()) {
				$this->productFacade->saveProductImage($product, $image->toImage(), Tools::getImageTypeFromFileUpload($image));
			}

//			if (isset($oldLogo)) {
//				$this->imageStorage->deleteImage($oldLogo);
//			}

			$this->onSuccess($product, $form['submitAndContinue']->isSubmittedBy());
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}


interface IProductControlFactory
{
	public function create(Product $product = null): ProductControl;
}
