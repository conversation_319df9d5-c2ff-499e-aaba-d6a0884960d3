@use "sass:color";
@use "tools";
@use "vars";

.k__title {
  font-size: 22px;
  font-weight: 600;
  line-height: 1.5;
  color: #000;

  @include tools.breakpoint(vars.$tablet) {
    font-size: 24px;
  }
}

.k__title.fw-700 {
  font-weight: 700;
}

.k__title a {
  color: #000;
}

.k__title a:hover {
  text-decoration: underline;
}

.k__text,
.k__text p {
  font-size: 12px;
  line-height: 1.5;
  color: #2c2c2c;

  @include tools.breakpoint(vars.$desktop) {
    font-size: 14px;
  }
}

.k__text a {
  text-decoration: underline;
}

.k__text a:hover {
  text-decoration: none;
}

.ot-sdk-show-settings {
  background: none;
  border: 0;
  padding: 0;
  color: #2c2c2c;
  cursor: pointer;
}

.ot-sdk-show-settings:hover {
  text-decoration: underline;
}

.k-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid vars.$color-primary;
  border-radius: 40px;
  font-weight: bold;
  font-size: 12px;
  line-height: 14px;
  padding: 1rem;
  color: #fff;
  background-color: vars.$color-primary;
  cursor: pointer;

  @include tools.breakpoint(vars.$tablet) {
    min-width: 220px;
  }
}

.k-button:hover {
  background-color: color.adjust(vars.$color-primary, $lightness: -10%);
}

.k-button.hide {
  display: none;
}

.author {
  //background-color: #f8f9fa;
  font-size: 30px;
  .author__container {
    padding: 48px 12px;
    width: 1170px;
    max-width: 100%;
    margin: auto;
  }
  .author__content {
    display: flex;
    align-items: center;
    gap: 40px;
    .author__avatar {
      border-radius: 50%;
      max-width: 160px;
      height: auto;
    }
    .author__title {
      font-size: 72px;
      font-weight: 600;
    }
    .author__articles-count {
      font-size: 14px;
      font-weight: 400;
    }
  }
}


.k-hp-swiper {
  //margin-top: 24px;
  padding: 12px;

  .swiper-button-disabled {
    display: none;
  }
  
  .swiper-button-prev {
    left: 1px;
  }
  .swiper-button-next {
    right: 6px;
  }
  
  
  .swiper-button-next, .swiper-button-prev {
    border-radius: 50%;
    background-color: white;
    padding: 21px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
    transition: box-shadow 0.2s ease;
    color: #bc2026;
  }
  
  .swiper-button-next:hover, .swiper-button-prev:hover {
    box-shadow: 0 1px 6px  #bc2026;
  }
  
  .swiper-button-next::after, .swiper-button-prev::after {
    font-weight: bold;
    font-size: 18px;
  }
  
  .img-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    width: 90px;
    height: 90px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
    transition: box-shadow 0.2s ease;
    border: 2px solid vars.$color-primary;
    overflow: hidden;
  }
  
  .img-wrapper:hover {
    cursor: pointer;
    box-shadow: 0 1px 6px #bc2026;
  }

  .swiper-slide {
    width: auto;
    opacity: 0;
  }  
  
  .swiper-slide img {
    max-width: 40px;
    height: auto;  
    
    @include tools.breakpoint(vars.$tablet) {
      max-width: 50px;
    }
  }
}

.swiper-initialized .swiper-slide {
    opacity: 1;
  }


@media only screen and (max-width: 500px) {
  .k-hp-swiper {
    //margin-top: 12px;
    padding: 12px 0px 12px 4px;

    .img-wrapper {
      width: 64px;
      height: 64px;
    }
    .swiper-button-next, .swiper-button-prev {
        display: none;
    }  
  } 

}
.faq-answer {
  display: none;
}

.faq-answer.show {
  display: block;
}

.faq-section {
  margin-top: 36px;

  .faq-section-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 12px;
  }

  .faq-question {
    padding: 16px 12px;
    cursor: pointer;
    border-radius: 6px;
    box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.2);
    margin-bottom: 16px;

    .faq-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      margin: 0;
    }

    .faq-answer {
      margin-top: 16px;      
      font-weight: 400;
      color: #000;
    }

    .arrow {
      margin-left: 32px;
      transition: transform 0.3s, fill 0.3s;
      fill: #bc2026;
    }

    .rotate {
      transform: rotate(180deg);
      fill: #bc2026; 
    }
  }

  .faq-question.show {
    border-radius: 6px;
    color: #000;
    background-color: rgba(255, 255, 255, 0.04);

    border-bottom: none;
  }

}

.faq-answer a {
  text-decoration: underline;
}

.faq-answer a:hover {
  text-decoration: none;
}

.faq-section.mt-3 {
  margin-top: 1rem;
}

@media only screen and (max-width: 768px) {
  .author {
    .author__content {
      gap: 20px;
      .author__avatar {
        width: 80px;
      }
      .author__title {
        font-size: 48px;
      }
    }
  }
}

@media only screen and (max-width: 360px) {
  .author {
    .author__content {
      flex-direction: column;
      text-align: center;

      .author__avatar {
        width: 130px;
      }
    }
  }

}

.ads-container {
  position: relative;
  padding-top: 20px;
  text-align: center;
  padding-bottom: 25px;
  min-height: 300px;  
}

.ads-container--mobile {
  display: block;
  min-height: 200px;

  @include tools.breakpoint(vars.$desktop) {
    display: none;
  }
}

.ads-container--desktop {
  display: none;

  @include tools.breakpoint(vars.$desktop) {
    display: block;
    min-height: 90px;
  }
}

.ads-container--branding {
  min-height: 0px;
  margin: 0;
  padding: 0;

  @include tools.breakpoint(vars.$desktop) {
    min-height: 250px;
  }
}

.ads-container--branding .ads-label{
  display: none;
}

.us-wrapper {
  position: relative;
  padding-top: 20px;
  text-align: center;
  padding-bottom: 25px;  
}

.us-wrapper--130 {
  height: 130px;
}

.ads-label {
  position: absolute;
  padding: 0 8px;
  width: 100%;
  color: #888;
  right: 0;
  top: 2px;
  font-size: 10px;
  line-height: 16px;
}

.order-first {
  order: -9999;
}

.order-sm-1 {
  @include tools.breakpoint(vars.$tablet) {
    order: 1;
  }
}

.nice-select {
  display: flex;
  align-items: center;
  background-color: transparent;

  @include tools.breakpoint(vars.$tablet) {
    margin-right: 1rem;
  }
}

.nice-select .current {
  display: inline-block;
  background: vars.$color-primary;
  padding: 0.25rem 0.5rem;
  border-radius: 8px;
  color: #fff;
  font-weight: bold;
  line-height: 1.5;
  font-size: 12px;
}

.nice-select__button {
  background: transparent;
  border: 1px solid vars.$color-primary;
  border-radius: 20px;
  margin-right: 4px;
  height: 32px;
  display: block;
  cursor: pointer;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 12px;
  color: vars.$color-primary;
}