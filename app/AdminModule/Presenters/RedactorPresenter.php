<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Images\ImageStorage;
use Nette\Http\FileUpload;
use Nette\InvalidArgumentException;
use Nette\Utils\Json;

class RedactorPresenter extends BasePresenter
{
	/** @var ImageStorage @inject */
	public $imageStorage;

	public function actionUploadImage()
	{
		$images = [];

		$files = $this->getHttpRequest()->getFiles();

		if (isset($files['file'])) {
			/** @var FileUpload $file */
			foreach ($files['file'] as $index => $file) {
				try {
					$id = 'file-' . ($index + 1);
					$images[$id] = [
						'url' => $this->imageStorage->saveImage($file, ImageStorage::NAMESPACE_REDACTOR),
						'id' => $id,
					];
				} catch (InvalidArgumentException $e) {
					 $images = [
						'error' => true,
						'message' => $e->getMessage(),
					 ];
					 break;
				}
			}
		}

		$this->sendJ<PERSON>($images);
	}
}
