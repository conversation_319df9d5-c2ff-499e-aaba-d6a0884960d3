<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Forms\LocalizationControl;

use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON>ino\Model\Localization\LocalizationFacade;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\InvalidArgumentException;

class LocalizationControl extends Control
{
	public $onSuccess = [];

	private $localization;

	private $localizationFacade;

	public function __construct(?Localization $localization, LocalizationFacade $localizationFacade)
	{
		$this->localization = $localization;
		$this->localizationFacade = $localizationFacade;
	}

	public function createComponentForm()
	{
		$form = new Form();

		$form->addText('name', 'Name:')
			->setDisabled()
			->setRequired(false);

		$form->addText('shortDateFormat', 'Short Date Format:')
			->setRequired(false);

		$form->addText('longDateFormat', 'Long Date Format:')
			->setRequired(false);

		$form->addSubmit('submit', 'Save')
			->setHtmlAttribute('class', 'btn btn-primary');

		$form->addSubmit('submitAndContinue', 'Save and continue')
			->setHtmlAttribute('class', 'btn btn-primary');

		if ($this->localization) {
			$form->setDefaults([
				'name' => $this->localization->getName(),
				'shortDateFormat' => $this->localization->getShortDateFormat(),
				'longDateFormat' => $this->localization->getLongDateFormat(),
			]);
		}

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			if ($this->localization) {
				$localization = $this->localization;
				$localization->setShortDateFormat($values->shortDateFormat ?: null);
				$localization->setLongDateFormat($values->longDateFormat ?: null);

				$this->localizationFacade->saveLocalization($localization);

				$this->onSuccess($localization, $form['submitAndContinue']->isSubmittedBy());
			} else {
				throw new InvalidArgumentException('Localization not found.');
			}
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function render()
	{
		$this->template->localization = $this->localization;
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}
