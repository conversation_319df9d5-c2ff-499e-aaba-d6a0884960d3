<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Geo\Repositories;

use Doctrine\ORM\Query\ResultSetMapping;
use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\Model\EntityRepository;
use <PERSON><PERSON>ino\Model\Geo\Entities\City;
use Ka<PERSON>ino\Model\Localization\Entities\Localization;
use Kaufino\Model\Shops\Entities\Shop;
use Kaufino\Model\Websites\Entities\Website;
use Tracy\Debugger;

class CityRepository extends EntityRepository
{
	public function getCities(Localization $localization = null): QueryBuilder
	{
		$qb = $this->createQueryBuilder('c');

		if ($localization) {
			$qb->andWhere('c.localization = :localization')->setParameter('localization', $localization);
		} else {
			$qb->leftJoin('c.localization', 'l');
		}

		return $qb;
	}

	public function findCityBySlug(Localization $localization, string $slug)
	{
		$qb = $this->getCities($localization)
			->andWhere('c.slug = :slug')
			->setParameter('slug', $slug);

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function findCities(Localization $localization, ?int $limit, ?string $websiteType = null)
	{
		$qb = $this->getCities($localization)
			->addOrderBy('c.population', 'DESC')
            ->andWhere('c.noIndex = false')
        ;

        if ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('c.activeOferto = true');
        } elseif($websiteType === null || $websiteType === Website::MODULE_KAUFINO) {
            $qb->andWhere('c.active = true');
        }

        if ($limit) {
            $qb->setMaxResults($limit);
        }

		return $qb->getQuery()->getResult();
	}

    public function findCitiesByLocalization(Localization $localization)
    {
        $qb = $this->getCities($localization)
            ->addOrderBy('c.population', 'DESC')
        ;

        return $qb->getQuery()->getResult();
    }

	public function findCitiesByShop(Shop $shop, int $limit, ?string $websiteType = null)
	{
		$qb = $this->getCities($shop->getLocalization())
			->leftJoin('c.shops', 's')
			->andWhere('s.id = :shopId')
			->addOrderBy('c.population', 'DESC')
			->setParameter('shopId', $shop->getId())
            ->andWhere('s.activeCities = true')
			->setMaxResults($limit);

        if ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('c.activeBrandsOferto = true');
        } else {
            $qb->andWhere('c.activeBrandsKaufino = true');
        }

		return $qb->getQuery()->getResult();
	}

    public function findCitiesByShops(array $shops, int $limit, ?string $websiteType = null)
    {
        if (empty($shops)) {
            return [];
        }

        $qb = $this->getCities($shops[0]->getLocalization())
            ->leftJoin('c.shops', 's')
            ->andWhere('s.id IN (:shopIds)')
            ->andWhere('c.active = true')
            ->addOrderBy('c.population', 'DESC')
            ->setParameter('shopIds', $shops)
            ->setMaxResults($limit);

        if ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('c.activeOferto = true');
        }

        return $qb->getQuery()->getResult();
    }

	public function findNearestCitiesByCity(City $city, int $limit, ?string $websiteType = null)
	{
		// @todo: predelat na DQL
		$query = $this->getEntityManager()->getConnection()->executeQuery(
			sprintf("SELECT id, name, lat, lng, SQRT(
                        POW(69.1 * (lat - %s), 2) +
                        POW(69.1 * (%s - lng) * COS(lat/ 57.3), 2)) AS distance
                    FROM kaufino_geo_city 
                    WHERE localization_id = %d AND id != %d AND " . ($websiteType === Website::MODULE_OFERTO ? "active_oferto = 1" : "active = 1") . " AND lat IS NOT NULL AND lng IS NOT NULL
                    HAVING distance < %s ORDER BY distance LIMIT %s", $city->getLat(), $city->getLng(), $city->getLocalization()->getId(), $city->getId(), 75, $limit)
		);

		$results = $query->fetchAllAssociative();

		$cityIds = [];
		foreach ($results as $result) {
			$cityIds[] = $result['id'];
		}

		$qb = $this->getCities($city->getLocalization())
			->andWhere('c.id IN (:cityIds)')
			->addOrderBy('c.population', 'DESC')
			->setParameter('cityIds', $cityIds)
            ->andWhere('c.noIndex = false')
			->setMaxResults($limit);

        if ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('c.activeOferto = true');
        }

        return $qb->getQuery()->getResult();
	}

    public function findCitiesToActivate(Localization $localization, string $websiteType, int $limit)
    {
        $qb = $this->getCities($localization)
            ->addOrderBy('c.population', 'DESC')
        ;

        if ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('c.activeOferto = false');
        } else {
            $qb->andWhere('c.active = false');
        }

        return $qb
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult()
        ;
    }

    public function findCitiesToActivateBrands(Localization $localization, string $websiteType, int $limit)
    {
        $qb = $this->getCities($localization)
            ->addOrderBy('c.population', 'DESC')
            ->setMaxResults($limit)
        ;

        if ($websiteType === Website::MODULE_OFERTO) {
            $qb
                ->andWhere('c.activeOferto = true')
                ->andWhere('c.activeBrandsOferto = false')
            ;
        } else {
            $qb
                ->andWhere('c.active = true')
                ->andWhere('c.activeBrandsKaufino = false')
            ;
        }

        return $qb->getQuery()->getResult();
    }

    public function findActiveCities(Localization $localization, string $websiteType)
    {
        $qb = $this->getCities($localization)
            ->andWhere('c.noIndex = false')
        ;

        if ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('c.activeOferto = true');
        } elseif ($websiteType === Website::MODULE_KAUFINO) {
            $qb->andWhere('c.active = true');
        }

        $qb->addOrderBy('c.population', 'DESC');

        return $qb->getQuery()->getResult();
    }

    public function findClosestCityId(Localization $localization, float $lat, float $lon)
    {
        return $this->getEntityManager()->getConnection()->executeQuery('
            SELECT
            id,
            lat,
            lng,
            (
                6371 * acos(
                    cos(radians(:lat)) * cos(radians(lat)) *
                    cos(radians(lng) - radians(:lng)) +
                    sin(radians(:lat)) * sin(radians(lat))
                )
            ) AS distance
        FROM kaufino_geo_city
        WHERE localization_id = :localization
        ORDER BY distance ASC
        LIMIT 1;
        ', [
            'localization' => $localization->getId(),
            'lat' => $lat,
            'lng' => $lon,
        ])->fetchOne();
    }

    public function findTopCityByPopulation(Localization $localization): ?City
    {
        return $this->getCities($localization)
            ->addOrderBy('c.population', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
}
