<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Commands;

use Ka<PERSON>ino\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON>ino\Model\Leaflets\Entities\LeafletPage;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;

final class ArchiveLeaflets extends Job
{
	/** @var LeafletFacade */
	private $leafletFacade;

	public function __construct(
		LeafletFacade $leafletFacade
	) {
		parent::__construct();

		$this->leafletFacade = $leafletFacade;
	}

	protected function configure(): void
	{
		$this->setName('kaufino:archive-leaflets');
	}

	public function start(): void
	{
		/** @var Leaflet $leaflet */
		foreach ($this->leafletFacade->findLeafletsToArchive() as $leaflet) {
			$countOfArchivedPages = $leaflet->getCountOfArchivedPages();

			$leaflet->archive();
			$this->leafletFacade->saveLeaflet($leaflet);

			/** @var LeafletPage $leafletPage */
			foreach ($leaflet->getPages() as $leafletPage) {
				if ($countOfArchivedPages && $leafletPage->getPageNumber() <= $countOfArchivedPages) {
					continue;
				}

				$leafletPage->setImageUrl(null);
				$this->leafletFacade->saveLeafletPage($leafletPage);
//				$leaflet->removeLeafletPage($leafletPage);
//				$this->leafletFacade->removeLeafletPage($leafletPage);
			}

			echo "Archivuji letak: " . $leaflet->getId() . ". Expirace: " . $leaflet->getValidTill()->format('Y-m-d H:i:s') . "\n";
		}
	}
}
