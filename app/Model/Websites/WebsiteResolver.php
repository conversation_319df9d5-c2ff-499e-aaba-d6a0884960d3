<?php

declare(strict_types=1);

namespace Ka<PERSON>ino\Model\Websites;

use <PERSON><PERSON><PERSON>\Model\Configuration;
use Ka<PERSON>ino\Model\Websites\Entities\Website;
use Ka<PERSON>ino\Model\Websites\Repositories\WebsiteRepository;
use Nette\Http\Request;
use Nette\Http\Url;
use Nette\Utils\Strings;
use Tracy\Debugger;

class WebsiteResolver
{
	private WebsiteRepository $websiteRepository;
	private Request $request;
    private Configuration $configuration;

    public function __construct(WebsiteRepository $websiteRepository, Request $request, Configuration $configuration)
	{
		$this->websiteRepository = $websiteRepository;
		$this->request = $request;
        $this->configuration = $configuration;
    }

    private function getDevDomains(): array
    {
        return [
            '127.0.0.1' => 'kaufino.com',
            'localhost' => 'kaufino.com',
            'kaufino.dev' => 'kaufino.com',
            'kaufino.czlocal' => 'kaufino.com',
            'letado.dev' => 'letado.com',
            'letado.czlocal' => 'letado.com',
            'letado.comlocal' => 'letado.com',
        ];
    }

    private function getDevDomain(string $domain, string $host): string
    {
        $devDomains = $this->getDevDomains();
        return $devDomains[$host] ?? $domain;
    }

    public function resolve(): ?Website
    {
        $url = $this->request->getUrl();
        $domain = $url->getDomain();
        $host = $url->getHost();

        if ($this->isSpecialTld($domain)) {
            $domain = $url->getDomain(3);
        }

        if ($this->configuration->isDevelopmentMode()) {
            if ($this->request->getCookie('domain')) {
                $domain = $this->request->getCookie('domain');
                $host = (new Url('https://' . $domain))->getHost();
            } else {
                $domain = $this->getDevDomain($domain, $host);
            }
        }

        if ($url->getDomain(3) === 'weekly-ads.mroferto.us') {
            $domain = 'https://weekly-ads.mroferto.us';
        }

        if ($url->getDomain(3) === 'catalogues123.co.za') {
            $domain = 'https://www.catalogues123.co.za';
        }

        if (Strings::contains($url->getDomain(), '123')) {
            $domain = 'https://www.' . $url->getDomain();

            if (Strings::startsWith($url->getDomain(3), 'www.') === false) {
                $domain = 'https://' . $url->getDomain(3);
            }
        }

        bdump($url->getDomain());
        bdump($url->getDomain(3));

        if ($website = $this->websiteRepository->findOneBy(['domain' => $domain])) {
            return $website;
        }

        [$domainPattern, $module] = $this->getDomainPatternAndModule($domain, $host);

        /** @var ?Website $website */
        $website = $this->websiteRepository->findOneBy([
            'module' => $module,
            'domain' => $domainPattern,
        ]);

        return $website;
    }

    private function isSpecialTld(string $domain): bool
    {
        $specialTlds = ['co.za', 'co.uk', 'com.br', 'com.tr'];
        foreach ($specialTlds as $tld) {
            if (Strings::contains($domain, $tld)) {
                return true;
            }
        }
        return false;
    }

    private function isInvalidRegion(): bool
    {
        if ($this->isOferito()) {
            $region = $this->getRegion();
            $invalidRegions = ['ads.txt', 'robots.txt'];
            $validRegions = ['cz', 'sk', 'de', 'pl', 'it', 'hu', 'ro', 'ca', 'us', 'fr'];

            if ($region !== null && !in_array($region, $invalidRegions)) {
                return true;
            }
        }
        return false;
    }

    private function getDomainPatternAndModule(string $domain, string $host): array
    {
        if (Strings::contains($domain, 'kaufino')) {
            return $this->getKaufinoPatternAndModule($host);
        } elseif (Strings::contains($domain, 'letado') || $this->isOferito() || $this->isMrOfferto()) {
            return $this->getLetadoPatternAndModule($host);
        } elseif ($domain === 'mroferto.com') {
            return ['https://www.mroferto.com/' . $this->getRegion() . '/', Website::MODULE_OFERTO_COM];
        } else {
            return ['https://www.' . $domain, Website::MODULE_OFERTO];
        }
    }

    private function getLetadoPatternAndModule(string $host): array
    {
        $hostWithoutWww = str_replace('www.', '', $host);
        $hostParts = explode('.', $hostWithoutWww);

        if (count($hostParts) > 2) {
            return ['https://' . $hostWithoutWww, Website::MODULE_LETADO_SUBDOMAIN];
        } else {
            return ['https://www.letado.com/' . $this->getRegion() . '/', Website::MODULE_LETADO];
        }
    }

    private function getKaufinoPatternAndModule(string $host): array
    {
        $hostWithoutWww = str_replace('www.', '', $host);
        $hostParts = explode('.', $hostWithoutWww);

        if (count($hostParts) > 2) {
            return ['https://' . $hostWithoutWww, Website::MODULE_KAUFINO_SUBDOMAIN];
        } else {
            return ['https://www.kaufino.com/' . $this->getRegion() . '/', Website::MODULE_KAUFINO];
        }
    }

	/**
	 * @return mixed|string
	 */
	private function getRegion()
	{
		$url = $this->request->getUrl()->getRelativeUrl();

        $url = str_replace('new/', '', $url);

		$arr = explode("/", $url, 2);

		return $arr[0] ?: null;
	}

    public function isOferito(): bool
    {
        $domain = $this->request->getUrl()->getDomain();

        if ($this->configuration->isDevelopmentMode()) {
            $domain = $this->request->getCookie('domain');
        }

        if ($domain === null) {
            return false;
        }

        return Strings::contains($domain, 'oferito');
    }

    public function isMrOfferto()
    {
        $domain = $this->request->getUrl()->getDomain();

        if ($this->configuration->isDevelopmentMode()) {
            $domain = $this->request->getCookie('domain');
        }

        if ($domain === null) {
            return false;
        }

        return Strings::contains($domain, 'mrofferto') || Strings::contains($domain, 'mr-offerto');
    }
}
