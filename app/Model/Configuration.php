<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model;

use Nette\Http\Request;
use Nette\Utils\Strings;

class Configuration
{
	/** @var string */
	private $mode;

	/** @var string */
	private $imagesPath;

	/** @var string */
	private $imagesUrl;

	/** @var string */
	private $steveApiUrl;

	/** @var string */
	private $steveApiToken;

	/** @var string */
	private $tipliApiUrl;

	/** @var string */
	private $tipliApiToken;

	/** @var string */
	private $azureApiToken;

	/** @var string */
	private $chatGPTApiToken;

	/** @var string */
	private $googleServiceAccountKey;

	/** @var Request */
	private $httpRequest;

	/** @var string */
	private $responseCacheDir;

	public function __construct(Request $httpRequest)
	{
		$this->httpRequest = $httpRequest;
	}

	public function setMode(string $mode)
	{
		$this->mode = $mode;
	}

	public function getMode(): string
	{
		return $this->mode;
	}

	public function isDevelopmentMode(): bool
	{
		return $this->mode === 'development' || $this->mode === 'dev';
	}

	public function getImagesPath(): string
	{
		if (PHP_SAPI === 'cli') {
			return __DIR__ . '/../../www/upload/';
		}

		return $this->imagesPath;
	}

	public function setImagesPath(string $imagesPath)
	{
		$this->imagesPath = $imagesPath;
	}

	public function getImagesUrl(): string
	{
		return $this->imagesUrl;
	}

	public function setImagesUrl(string $imagesUrl)
	{
		$this->imagesUrl = $imagesUrl;
	}

	public function getSteveApiUrl(): string
	{
		return $this->steveApiUrl;
	}

	public function setSteveApiUrl(string $steveApiUrl)
	{
		$this->steveApiUrl = $steveApiUrl;
	}

	public function getSteveApiToken(): string
	{
		return $this->steveApiToken;
	}

	public function setSteveApiToken(string $steveApiToken)
	{
		$this->steveApiToken = $steveApiToken;
	}

	public function getTipliApiUrl(): string
	{
		return $this->tipliApiUrl;
	}

	public function setTipliApiUrl(string $tipliApiUrl)
	{
		$this->tipliApiUrl = $tipliApiUrl;
	}

	public function getTipliApiToken(): string
	{
		return $this->tipliApiToken;
	}

	public function setTipliApiToken(string $tipliApiToken)
	{
		$this->tipliApiToken = $tipliApiToken;
	}

	public function getAzureApiToken(): string
	{
		return $this->azureApiToken;
	}

	public function setAzureApiToken(string $azureApiToken)
	{
		$this->azureApiToken = $azureApiToken;
	}

	public function isLetado(): bool
	{
		return Strings::contains($this->httpRequest->getUrl()->getBaseUrl(), 'letado');
	}

	public function isOferto(): bool
	{
		return Strings::contains($this->httpRequest->getUrl()->getBaseUrl(), 'oferto');
	}

	public function getChatGPTApiToken(): string
	{
		return $this->chatGPTApiToken;
	}

	public function setChatGPTApiToken(string $chatGPTApiToken): void
	{
		$this->chatGPTApiToken = $chatGPTApiToken;
	}

	public function setResponseCacheDir(string $responseCacheDir): void
	{
		$this->responseCacheDir = $responseCacheDir;
	}

	public function getResponseCacheDir(): string
	{
		return $this->responseCacheDir;
	}

	public function getGoogleServiceAccountKey(): ?string
	{
		return $this->googleServiceAccountKey;
	}

	public function setGoogleServiceAccountKey(?string $googleServiceAccountKey): void
	{
		$this->googleServiceAccountKey = $googleServiceAccountKey;
	}
}
