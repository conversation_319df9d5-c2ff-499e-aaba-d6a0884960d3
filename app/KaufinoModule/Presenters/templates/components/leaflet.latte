{var $shop = $leaflet->getShop()}

<div class="k-leaflets__item mb-3 mb-sm-5 {isset($cssClass) ? $cssClass : ''}" n:attr="data-brochure-id: $leaflet->getOfferistaBrochureId() ?? null">
    {if $user->isLoggedIn()}
        <div class="k-leaflets__bubble">            
            {if $leaflet->isTop()}<span class="k-leaflets__bubble-top">TOP</span>{/if}
            {if $leaflet->isPrimary()}<span class="k-leaflets__bubble-primary">Primary</span>{/if}
            <small>{$leaflet->getPriority()}</small>
        </div>
    {/if}
    <a n:href="Leaflet:leaflet $shop, $leaflet" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} position-relative">
    <picture n:if="$leaflet->getFirstPage()">
        <source
            type="image/webp"
            srcset="
                {$leaflet->getFirstPage()->getImageUrl() |image:165,203,'exactTop','webp'} 165w,
                {$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop','webp'} 230w,
                {$leaflet->getFirstPage()->getImageUrl() |image:330,406,'exactTop','webp'} 2x
            "            
            sizes="(max-width: 767px) 165px, 230px"
        >
        <img
            src="{$leaflet->getFirstPage()->getImageUrl() |image:165,203,'exactTop'}"
            srcset="
                {$leaflet->getFirstPage()->getImageUrl() |image:165,203,'exactTop'} 165w,
                {$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop'} 230w,
                {$leaflet->getFirstPage()->getImageUrl() |image:330,406,'exactTop'} 2x
            "
            sizes="(max-width: 767px) 165px, 230px"
            width="230"
            height="288"
            alt="{if strtolower($leaflet->getName()) === strtolower($shop->getName())}{_'kaufino.leaflet.brandLeafletFrom', [brand => $shop->getName()]} {_'app.day.' . $leaflet->getValidSince()->format('N') . '.genitive'} {$leaflet->getValidSince()|localDate:'short'}{else}{$leaflet->getName()}{/if}"
            class="k-leaflets__image {if $leaflet->isExpired()}grayscale{/if}"
            loading="lazy"
            fetchpriority="auto"
            decoding="async"
        >
    </picture>
    <div class="blur-img blur-background">
        <picture>
            <source
                srcset="
                    {$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop','webp'} 230w                    
                "
                type="image/webp"
            >
            <img src="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop'}" class="w100 h100 object-fit-cover {if $leaflet->isExpired()}grayscale{else}blur-image{/if}" loading="lazy" fetchpriority="auto" decoding="async" alt="blur background">
        </picture>
    </div>    
    </a>
    
    <span n:if="isSet($validBadgeShow) && $validBadgeShow == true && $leaflet->isValid() && $leaflet->isPrimary()" class="k-leaflets__corner"><span>{_'kaufino.leaflet.valid'}</span></span>

        <div class="k-leaflets__info-wrapper">
            <div style="display: flex; flex-direction: column; flex: 1;">
                <div class="k-leaflets__info">
                <img src="{$shop->getLogoUrl() |image:80,80}" alt="{$shop->getName()}" loading="lazy">
                <div class="k-leaflets__title mt-0 mb-0">
                    <a n:href="Leaflet:leaflet $shop, $leaflet" class="color-black">
                        {if $leaflet->isChecked() === false}
                            {_kaufino.leaflet.titleUnChecked, [brand => $leaflet->getName()]|noescape}
                        {elseif false && strtolower($leaflet->getName()) === strtolower($shop->getName())}
                            {_'kaufino.leaflet.brandLeafletFrom', [brand => $shop->getName()]} {_'app.day.' . $leaflet->getValidSince()->format('N') . '.genitive'} {$leaflet->getValidSince()|localDate:'short'}
                        {else}
                            {$leaflet->getName()}
                        {/if}
                    </a>
                </div> 
            </div>
            <p class="k-leaflets__date mt-0 mb-0" n:if="$leaflet->isChecked()">{if $localization->isHungarian()}{$leaflet->getValidSince()|localDate:'long'} – {$leaflet->getValidTill()|localDate}{else}{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}{/if}</p>
            </div>
        </div>
    <a n:if="isSet($buttonShow) && $buttonShow == true" n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="k-leaflets__button k-leaflets__button--secondary mt-3">{_kaufino.shop.showLeaflet}</a>
</div>