# Google Indexing API Implementation

## Přehled

Implementace automatického indexování letáků do Google pomocí Google Indexing API a Search Console API.

## Funkční logika

1. **Přidání URL do fronty**: Vloží URL letáků do tabulky `kaufino_seo_google_index`, pokud ještě nejsou v DB
2. **Indexování**: Pokud `is_indexed = 0`, pošle URL do Google Indexing API
3. **Kontrola stavu**: Pravidelně (každých 6 hodin) kontroluje stav indexace přes Search Console API
4. **Aktualizace výsledků**: Výsledky aktualizuje v DB
5. **Označení indexovaných**: Po úspěšné indexaci nastaví `is_indexed = 1`
6. **Logování**: Loguje každé API volání do `kaufino_seo_google_index_process`
7. **Rate limiting**: Respektuje denní (200) a minutové (600) limity Google API

## Databázové tabulky

### kaufino_seo_google_index
- `id` - prim<PERSON><PERSON><PERSON> k<PERSON>
- `website_id` - odkaz na website
- `url` - URL k indexování (unique)
- `is_indexed` - zda je URL indexováno
- `last_indexing_request_at` - kdy byl poslán poslední request
- `last_status_check_at` - kdy byla provedena poslední kontrola stavu
- `indexed_at` - kdy bylo URL indexováno
- `last_indexing_status` - poslední stav ('requested', 'indexed', 'not_indexed')
- `last_error_message` - poslední chybová zpráva
- `request_count` - počet requestů
- `created_at`, `updated_at` - časové značky

### kaufino_seo_google_index_process
- `id` - primární klíč
- `google_index_id` - odkaz na GoogleIndex
- `api_type` - typ API ('indexing' nebo 'search_console')
- `request_type` - typ requestu ('URL_UPDATED', 'URL_DELETED', 'status_check')
- `request_data` - data requestu (JSON)
- `response_data` - odpověď API (JSON)
- `http_status_code` - HTTP status kód
- `success` - zda byl request úspěšný
- `error_message` - chybová zpráva
- `created_at` - časová značka

## Konfigurace

### 1. Google Service Account

Vytvořte Google Service Account s přístupem k:
- Google Indexing API
- Google Search Console API

### 2. Konfigurace v local.neon

```neon
parameters:
    google:
        serviceAccountKey: '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
```

## Spuštění

### Manuální spuštění
```bash
bin/helper in
php bin/console kaufino:process-google-index
```

### Automatické spuštění (cron)
```bash
# Každých 30 minut
*/30 * * * * cd /path/to/project && bin/helper in && php bin/console kaufino:process-google-index
```

## Monitoring

### Statistiky
Command zobrazuje statistiky:
- Počet URL čekajících na indexování
- Počet úspěšně indexovaných URL
- Počet API requestů dnes/za minutu
- Limity API

### Logy
- Všechny API volání se logují do `kaufino_seo_google_index_process`
- Chyby se logují do Tracy logů (`log/google-indexing.log`)

## API Limity

- **Denní limit**: 200 requestů
- **Minutový limit**: 600 requestů
- System automaticky respektuje tyto limity

## Troubleshooting

### Časté problémy

1. **"Google Service Account Key is not configured"**
   - Zkontrolujte konfiguraci `google.serviceAccountKey` v local.neon

2. **"Failed to get Google access token"**
   - Zkontrolujte platnost Service Account credentials
   - Ověřte, že Service Account má správná oprávnění

3. **Rate limit exceeded**
   - System automaticky čeká, ale můžete snížit frekvenci spouštění

4. **URL not being indexed**
   - Google může trvat několik dní než URL indexuje
   - Zkontrolujte, zda je URL dostupné a validní

### Debug

Pro debug můžete použít:
```php
// V ProcessGoogleIndex.php
Debugger::dump($this->googleIndexFacade->getStatistics());
```

## Rozšíření

### Přidání nových typů URL

1. Upravte `generateLeafletUrls()` v `ProcessGoogleIndex.php`
2. Přidejte nové URL generování podle potřeby

### Změna frekvence kontroly

Upravte konstanty v `GoogleIndex.php`:
- `needsStatusCheck()` - frekvence kontroly stavu
- `needsIndexingRequest()` - frekvence posílání requestů
