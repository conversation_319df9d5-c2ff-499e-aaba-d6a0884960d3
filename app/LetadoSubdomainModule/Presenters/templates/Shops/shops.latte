{block scripts}
    {include parent}
{/block}

{block description}{_"$websiteType.shops.text"}{/block}

{block content}
<div class="container">
    <div class="mb-6">
        <h1 n:block="title" class="k__title ta-center mt-5 mb-4">{_"$websiteType.shops.title"}</h1>
        <p class="k__text ta-center mw-700 mb-0">{_"$websiteType.shops.text"}</p>
    </div>
    
    <div class="k-shop">    
        {foreach $shops as $shop}
            <a n:href="Shop:shop $shop" class="k-shop__item">
                <span class="k-shop__image-wrapper">
                    <picture>
                        <source data-srcset="{$shop->getLogoUrl() |image:80,70,'fit','webp'}" type="image/webp">
                        <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$shop->getLogoUrl() |image:80,70}" width="80" height="70" alt="{$shop->getName()}" class="lazyload">
                    </picture>
                </span>
                <small class="k-shop__title">{$shop->getName()}</small>
            </a>
        {/foreach}
    </div>
</div>
