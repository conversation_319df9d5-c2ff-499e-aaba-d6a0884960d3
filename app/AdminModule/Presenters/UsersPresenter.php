<?php

namespace <PERSON><PERSON><PERSON>\AdminModule\Presenters;

use <PERSON><PERSON><PERSON>\AdminModule\Forms\UserControl\UserControl;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\UserControl\UserControlFactory;
use <PERSON><PERSON><PERSON>\Model\Users\UserFacade;

class UsersPresenter extends BasePresenter
{
    /** @var UserFacade @inject */
    public $userFacade;

    /** @var UserControlFactory @inject */
    public $userControlFactory;

    public function createComponentUsersGrid($name)
    {
        $users = $this->userFacade->getUsers();

        $grid = $this->dataGridFactory->create()
            ->getGrid($this, $name, $users);

        $grid->addColumnText('email', 'Email')
            ->setFilterText();

        $grid->addColumnText('name', 'Name')
            ->setFilterText();

        $grid->addColumnText('surname', 'Surname')
            ->setFilterText();

        $grid->addColumnText('slug', 'Slug')
            ->setFilterText();

        $grid->addColumnText('imageUrl', 'Profile image')
            ->setRenderer(function ($row) {
                return $row->getImageUrl() ? '<img src="' . $row->getImageUrl() . '" width="50" height="50">' : '';
            })->setTemplateEscaping(false)->setAlign('center');

        $grid->addColumnText('active', 'Active')->setRenderer(function ($row) {
            return $row->isActive() ? '<i class="fa fa-check-circle text-success">' : '<i class="fa fa-times-circle text-danger">';
        })->setTemplateEscaping(false)->setAlign('center');

        $grid->addAction('user', '', 'user')
            ->setClass('btn btn-xs btn-primary')
            ->setIcon('pencil')
        ;
    }

    public function actionUser($id = null)
    {
        if ($id) {
            $user = $this->userFacade->find($id);

            if (empty($user)) {
                $this->error('User not found.');
            }

            $this->template->user = $user;
        }
    }

    protected function createComponentUserControl(): UserControl
    {
        $user = $this->getParameter('id') ? $this->userFacade->find($this->getParameter('id')) : null;
        $control = $this->userControlFactory->create($user);

        $control->onSuccess[] = function ($entity, $continue) use ($user) {
            $this->flashMessage('Successfully saved.');
            if ($user && !$continue) {
                $this->redirect(':Admin:Users:default');
            } else {
                $this->redirect(':Admin:Users:user', $entity->getId());
            }
        };

        return $control;
    }
}