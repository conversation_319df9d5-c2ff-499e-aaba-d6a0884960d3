{var $currentLeaflet = $shop->getCurrentLeaflet()}
{var $nextLeaflet = $shop->getNextLeaflet()}

{var $parameters = [
'currentLeafletFromDate' => $currentLeaflet ? $currentLeaflet->getValidSince() : null,
'currentLeafletToDate' => $currentLeaflet ? $currentLeaflet->getValidTill() : null,
'nextLeafletFromDate' => $nextLeaflet ? $nextLeaflet->getValidSince() : null,
'shopName' => $shop->getName(),
]}

{block title}
    {if $pageExtension && $pageExtension->getTitle()}
        {$seoGenerator->renderInSandbox($pageExtension->getTitle(), $parameters)}
    {else}
        {$metaTitle}
    {/if}
{/block}

{block description}{if $pageExtension && $pageExtension->getDescription()}{$seoGenerator->renderInSandbox($pageExtension->getDescription(), $parameters)}{else}{$metaDescription}{/if}{/block}

{block head}
    {include parent}          
    
    <!-- oferto.com / sticky -->
    {include "../components/sticky.latte"}                                     
{/block}

{block scripts}
    {include parent}

    <script type="application/ld+json" n:if="count($leaflets)">
        {
            "@context": "https://schema.org",
            "@type": "OfferCatalog",
            "name": {_"$websiteType.homepage.h1"},
            "url": {$presenter->link('//this')},
            "itemListElement": [
                {foreach $leaflets as $key => $leaflet}
                    {
                        "@type": "SaleEvent",
                        "name": {$leaflet->getName()},
                        "url": {$presenter->link('Leaflet:leaflet', $leaflet->getShop(), $leaflet)},
                        "startDate": {$leaflet->getValidSince()|date:'Y-m-d'},
                        "endDate": {$leaflet->getValidTill()|date:'Y-m-d'},
                        "location": {
                            "@type": "Place",
                            "name": {$leaflet->getShop()->getName()},
                            "url": {$presenter->link('Shop:shop', $leaflet->getShop())}
                        }
                    }{sep},{/sep}
        {/foreach}
        ]
    }
    </script>
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": {_oferto.navbar.home},
                    "item": {link //Homepage:default}
                },
                {
                    "@type": "ListItem",
                    "position": 2,
                    "name": {_kaufino.navbar.leaflets},
                    "item": {link //Leaflets:leaflets}
                },
                {
                    "@type": "ListItem",
                    "position": 3,
                    "name": {$shop->getTag()->getName()},
                    "item": {link //Tag:tag $shop->getTag()}
                },
                {
                    "@type": "ListItem",
                    "position": 4,
                    "name": {$shop->getName()},
                    "item": {link //this}
                }
        ]
        }
    </script>

    <script n:if="$faqContentBlocks" type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
                {foreach $faqContentBlocks as $faq} {
                    "@type": "Question",
                    "name": {$faq->getHeading()},
                    "acceptedAnswer": {
                        "@type": "Answer",
                        "text": {strip_tags($faq->getContent())}
                    }
                }{sep},{/sep}
        {/foreach}
            ]
        }
    </script>

    <script>
        const elementsWithBrochureId = document.querySelectorAll('[data-brochure-id]');

        if (elementsWithBrochureId.length > 0) {
            if (checkCookie('userLocation') === false && navigator.geolocation) {
                getLocationFromBrowser();
            }
        }

        elementsWithBrochureId.forEach(element => {
            const brochureId = element.dataset.brochureId;

            const xhr = new XMLHttpRequest();
            xhr.open('POST', {link :Api:Offerista:brochureImpression}, true);
            xhr.setRequestHeader('Content-Type', 'application/json');

            xhr.send(JSON.stringify({ brochureId: brochureId }));
        });
    </script>
{/block}

{define cities}
    <div n:if="$shop->isStore() && count($cities) > 0">
        <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">
            {_kaufino.shop.city, [brand => $shop->getName()]}
        </h2>

        <p class="k-tag mb-5">
            {foreach $cities as $city}
                <span class="k-tag__inner {$iterator->counter > 24 ? 'hidden' : ''}">
                    <a n:href="City:shop $city, $shop" class="k-tag__item">{$shop->getName()} {$city->getName()}</a>
                </span>
            {/foreach}
        </p>

        <p n:if="$shop->isStore() && count($cities) > 23" class="d-flex">
            <button class="link ml-auto k-show-more-button js-show-tag js-show-all-btn">{_'kaufino.showMore.cities'} »</button>
            <a n:href="Cities:cities" class="link ml-auto hidden k-show-more-button js-all-btn">{_'kaufino.showMore.allCities'} »</a>
        </p>
    </div>
{/define}

{block content}

{var $actualLeaflets = 4}
{var $actualShops = 30}
{var $actualActiveLeaflets = 42}

<div class="container">
    <div class="flex flex-col lg:flex-row lg:items-center justify-between pt-[34px] mb-11">
        <div class="flex flex-row items-center md:items-start gap-[15px] mb-3 lg:mb-0">
            <picture>
                <source srcset="{$shop->getLogoUrl() |image:160,140,'fit','webp'}" type="image/webp">
                <img src="{$shop->getLogoUrl() |image:160,140}" width="160" height="140" alt="{$shop->getName()}" class="w-[60px] h-[60px] rounded">
            </picture>            

            <div>
                <h1 class="text-[24px] font-medium leading-[34px]">
                    {if $pageExtension && $pageExtension->getHeading()}
                        {var $heading = $getHeadingFromPageExtension($pageExtension)}
                        {$heading}
                    {else}
                        {$heading1}
                    {/if}
                </h1>
                <div class="hidden md:flex gap-[9px] items-center font-light text-sm text-[#646C7C]">
                    <a n:href="Homepage:default">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
                            <path d="M2.6 7.54682V12H5.8V8.76132C5.8 8.54654 5.88429 8.34062 6.0343 8.18878C6.18432 8.03694 6.38784 7.95165 6.6 7.95165H7.4C7.61216 7.95165 7.81563 8.03694 7.96571 8.18878C8.11574 8.34062 8.2 8.54654 8.2 8.76132V12H11.4V7.54682M1 6.73715L6.43414 1.23733C6.50843 1.16209 6.59664 1.1024 6.69371 1.06168C6.79083 1.02096 6.89488 1 7 1C7.10512 1 7.20918 1.02096 7.30624 1.06168C7.40336 1.1024 7.49158 1.16209 7.56587 1.23733L13 6.73715" stroke="#80899C" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                    </a>
                    
                    <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                        <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                    </svg>

                    <a n:href="Shops:shops">                        
                        {_"$websiteType.navbar.shops"}
                    </a>

                    <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                        <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                    </svg>

                    <a n:if="$shop->getTag() !== null" n:href="Tag:tag $shop->getTag()">                        
                        {$shop->getTag()->getName()}
                    </a>                    

                    <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                        <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                    </svg>

                    <span>{$shop->getName()}</span>
                </div>
            </div>
        </div>

        <div class="flex items-center gap-5 text-sm leading-[24.5px] text-[#646C7C] font-light py-3 pl-[21px] pr-[33px] bg-light-6 rounded-lg transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
            <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28" fill="none">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M6.56252 14.0001C6.56252 9.58103 9.60595 5.39453 14 5.39453C18.3941 5.39453 21.4375 9.58103 21.4375 14.0001C21.4375 16.4743 21.8327 17.9988 22.1922 18.8743C22.3721 19.3122 22.5443 19.5913 22.6575 19.7488C22.7142 19.8277 22.7566 19.877 22.7785 19.9009C22.7851 19.9081 22.7899 19.9131 22.7927 19.9159C22.8111 19.9328 22.8289 19.9504 22.8458 19.9688C22.9017 20.0291 22.9476 20.0956 22.9834 20.1662C23.0414 20.2801 23.0752 20.4084 23.078 20.5444C23.0787 20.5808 23.0772 20.6174 23.0735 20.6537C23.0698 20.6892 23.0639 20.7245 23.0559 20.7594C23.0248 20.8949 22.9608 21.025 22.8631 21.1372C22.7582 21.2576 22.6277 21.3424 22.4867 21.3906C22.3906 21.4234 22.2896 21.4392 22.1887 21.4375H5.81165C5.76516 21.4383 5.71868 21.4354 5.67267 21.4288C5.61541 21.4207 5.55994 21.407 5.5068 21.3883C5.36578 21.3389 5.23554 21.2527 5.13151 21.1309C4.98555 20.96 4.91689 20.7491 4.92216 20.5408C4.92547 20.4049 4.95976 20.2767 5.01822 20.163C5.05542 20.0905 5.10329 20.0223 5.16163 19.9608C5.17799 19.9436 5.19505 19.927 5.21276 19.9111C5.21541 19.9084 5.21977 19.9039 5.2257 19.8975C5.24696 19.8746 5.2888 19.8267 5.345 19.7492C5.45709 19.5947 5.62872 19.3191 5.80819 18.8836C6.16705 18.0126 6.56252 16.4887 6.56252 14.0001ZM20.5734 19.539C20.5943 19.5899 20.6152 19.6394 20.6362 19.6875H7.36812C7.38752 19.6429 7.4069 19.5972 7.42622 19.5503C7.88767 18.4303 8.31252 16.673 8.31252 14.0001C8.31252 10.2162 10.8735 7.14453 14 7.14453C17.1266 7.14453 19.6875 10.2162 19.6875 14.0001C19.6875 16.6601 20.1126 18.4168 20.5734 19.539ZM13.2344 23.8438C13.2344 23.3605 12.8426 22.9688 12.3594 22.9688C11.8761 22.9688 11.4844 23.3605 11.4844 23.8438C11.4844 24.5109 11.7493 25.1508 12.2212 25.6227C12.693 26.0945 13.3329 26.3594 14 26.3594C14.6671 26.3594 15.307 26.0945 15.7789 25.6227C16.2507 25.1508 16.5156 24.5109 16.5156 23.8438C16.5156 23.3605 16.1239 22.9688 15.6406 22.9688C15.1574 22.9688 14.7656 23.3605 14.7656 23.8438C14.7656 24.0469 14.685 24.2417 14.5414 24.3852C14.3979 24.5287 14.2031 24.6094 14 24.6094C13.7969 24.6094 13.6021 24.5287 13.4586 24.3852C13.3151 24.2417 13.2344 24.0469 13.2344 23.8438Z" fill="#646C7C"/>
            </svg>
            <div>
                <span class="font-medium">Upozornit,</span> až vyjde nový leták
            </div>
        </div>
    </div>

    <div class="flex flex-row items-center gap-3 justify-between mb-5 md:mb-[35px] overflow-x-auto mr-[-20px] md:mr-0">
        <a n:href="shop, $shop" class="px-3 md:px-0 tab-item flex items-center justify-center pb-[5px] gap-2.5 font-medium leading-7 w-full hover:cursor-pointer text-primary transition-colors duration-200 border-b-2 border-b-primary group">
            <svg class="text-primary transition-colors duration-200" xmlns="http://www.w3.org/2000/svg" width="19" height="18" viewBox="0 0 19 18" fill="none">
                <path d="M9.50078 17V3.24709C9.50078 3.24709 7.64605 1.09329 1.39287 1.00641C1.34133 1.00588 1.29019 1.01609 1.24246 1.03642C1.19473 1.05676 1.15135 1.08684 1.11485 1.12488C1.04125 1.20268 0.999987 1.3077 1.00001 1.41711V14.3494C0.999175 14.4559 1.03823 14.5585 1.10883 14.6352C1.17942 14.7119 1.27595 14.7568 1.37776 14.7601C7.64454 14.8446 9.50078 17 9.50078 17ZM9.50078 17C9.50078 17 11.357 14.8446 17.6222 14.7601C17.7241 14.7568 17.8206 14.7119 17.8912 14.6352C17.9618 14.5585 18.0008 14.4559 18 14.3494V1.4171C18 1.30768 17.9587 1.20266 17.8852 1.12487C17.8488 1.08701 17.8057 1.05705 17.7582 1.03671C17.7108 1.01638 17.6599 1.00607 17.6086 1.0064C16.9363 1.01588 13.0878 0.729878 9.68813 3.0993M7.23444 8.35548C5.94879 7.90348 4.61353 7.62349 3.26057 7.52224M7.23444 11.6687C5.94851 11.2185 4.61331 10.9398 3.26057 10.8394M11.7672 8.35548C13.0528 7.90348 14.3881 7.62349 15.7411 7.52224M11.7672 11.6687C13.0531 11.2185 14.3883 10.9398 15.7411 10.8394" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            Letáky
        </a>

        <a n:href="offers, $shop" class="px-3 md:px-0 tab-item flex items-center justify-center pb-[5px] gap-2.5 font-medium leading-7 w-full hover:cursor-pointer text-current hover:text-primary transition-colors duration-200 hover:border-b-2 hover:border-b-primary group">
            <svg class="text-current group-hover:text-primary transition-colors duration-200" xmlns="http://www.w3.org/2000/svg" width="19" height="18" viewBox="0 0 19 18" fill="none">
                <path d="M16.7774 7.4V15.4C16.7774 16.2837 16.0525 17 15.1584 17H3.82503C2.93086 17 2.20599 16.2837 2.20599 15.4V7.4M3.0481 1C2.88731 1.00648 2.73211 1.06017 2.60235 1.15421C2.47257 1.24826 2.37411 1.37839 2.31953 1.528L1.09715 4.952C0.967617 5.32259 0.967617 5.72541 1.09715 6.096C1.22872 6.52073 1.49149 6.89412 1.84862 7.16383C2.20574 7.43354 2.63933 7.58606 3.08858 7.6C3.37992 7.58969 3.66635 7.52271 3.93149 7.40291C4.19662 7.28311 4.43525 7.11284 4.63374 6.90182C4.83224 6.69081 4.98669 6.44319 5.08826 6.17314C5.18984 5.9031 5.23655 5.6159 5.22572 5.328C5.20382 5.90812 5.41569 6.47314 5.81483 6.89908C6.21397 7.32502 6.76778 7.5771 7.35475 7.6C7.6461 7.58969 7.93251 7.52271 8.19771 7.40291C8.46283 7.28311 8.70148 7.11284 8.89997 6.90182C9.09839 6.69081 9.25285 6.44319 9.35444 6.17314C9.45604 5.9031 9.50275 5.6159 9.4919 5.328C9.48105 5.6159 9.52776 5.9031 9.62936 6.17314C9.73095 6.44319 9.88541 6.69081 10.0839 6.90182C10.2824 7.11284 10.521 7.28311 10.7862 7.40291C11.0513 7.52271 11.3377 7.58969 11.629 7.6C12.2132 7.57299 12.7629 7.31913 13.1586 6.89364C13.5543 6.46815 13.7639 5.90547 13.7419 5.328C13.7311 5.61721 13.7783 5.90567 13.881 6.17674C13.9835 6.44781 14.1394 6.69612 14.3396 6.90734C14.5398 7.11857 14.7804 7.28852 15.0473 7.40741C15.3143 7.52629 15.6025 7.59174 15.8952 7.6C16.3473 7.58937 16.7845 7.43837 17.1448 7.16842C17.5052 6.89846 17.7704 6.52326 17.9029 6.096C18.0324 5.72541 18.0324 5.32259 17.9029 4.952L16.6643 1.528C16.6097 1.37839 16.5113 1.24826 16.3815 1.15421C16.2517 1.06017 16.0965 1.00648 15.9357 1H3.0481Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            {_oferto.shop.offers.title, ['shopName' => $shop->getName()]}
        </a>

        <a n:href="stores, $shop" class="px-3 md:px-0 tab-item flex items-center justify-center pb-[5px] gap-2.5 font-medium leading-7 w-full hover:cursor-pointer text-current hover:text-primary transition-colors duration-200 hover:border-b-2 hover:border-b-primary group">
            <svg class="text-current group-hover:text-primary transition-colors duration-200" xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
                <path d="M2.02263 13.3117C3.50804 14.8389 5.53916 15.7136 7.66941 15.7437C9.79964 15.7737 11.8546 14.9565 13.3825 13.4718C14.8671 11.9438 15.6842 9.88875 15.654 7.75851C15.6239 5.62827 14.7489 3.59719 13.2216 2.11187M11.7669 7.79902H12.8983M10.0661 10.0648L11.199 11.1977M7.8003 11.7641V12.8955M1.34289 13.9884C1.22628 14.105 1.13562 14.245 1.07686 14.399C1.01809 14.5531 0.992539 14.7179 1.00188 14.8825C1.01122 15.0472 1.05524 15.208 1.13106 15.3544C1.20688 15.5009 1.31278 15.6296 1.44183 15.7323C3.40212 17.3072 5.87129 18.1096 8.38292 17.988C10.8945 17.8664 13.2746 16.8291 15.0735 15.0722C16.8296 13.2735 17.8663 10.8939 17.9879 8.38306C18.1095 5.87214 17.3076 3.4036 15.7336 1.44352C15.6312 1.31426 15.5026 1.20811 15.3562 1.13202C15.2099 1.05593 15.0492 1.01162 14.8845 1.002C14.7199 0.992369 14.5551 1.01764 14.4008 1.07616C14.2466 1.13468 14.1065 1.22512 13.9897 1.34156L1.34289 13.9884Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            {_oferto.shop.stores.title, ['shopName' => $shop->getName()]}
        </a>

        <div class="px-3 md:px-0 tab-item flex items-center justify-center pb-[5px] gap-2.5 font-medium leading-7 w-full hover:cursor-pointer text-current hover:text-primary transition-colors duration-200 hover:border-b-2 hover:border-b-primary group">
            <svg class="text-current group-hover:text-primary transition-colors duration-200" xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
                <path d="M18 9.5H9.4623M18 9.5L14.8264 6.28889M18 9.5L14.8264 12.7111M1.5387 15.3556C1.91647 16.9422 3.27646 18 4.86311 18H5.54311C6.14755 18 6.67643 17.4711 6.67643 16.8667V13.9956C6.67643 13.3911 6.14755 12.8622 5.54311 12.8622C4.93867 12.8622 4.40978 12.3333 4.40978 11.7289V7.19556C4.40978 6.59111 4.93867 6.06222 5.54311 6.06222C6.14755 6.06222 6.67643 5.53333 6.67643 4.92889V2.13333C6.67643 1.52889 6.14755 1 5.54311 1H4.86311C3.27646 1 1.91647 2.13333 1.5387 3.64444C0.858708 6.96889 0.783153 11.9556 1.5387 15.3556Z" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            Otevírací doba
        </div>
    </div>                       

    {* Letáky *}
    {if count($leaflets) > 0}
        {if $channel == 'c1' && $localization->isCzech()}
            <!-- Medium rectangle -->
            <div id="protag-medium_rectangle"></div>
            <script type="text/javascript">
            window.googletag = window.googletag || { cmd: [] };
            window.protag = window.protag || { cmd: [] };
            window.protag.cmd.push(function () {
                window.protag.display("protag-medium_rectangle");
            });
            </script>

        {elseif $channel !== 'e1' && $channel !== 'e2' && $channel !== 'e3'}
            {if $website->hasAdSense()}        
                <div>              
                    <!-- oferto.com / mobile_rectangle1 -->
                    {include "../components/mobile_rectangle1.latte"}
                </div> 
            {/if}  
        {/if}     

        {var $leaflet = $leaflets[0] ?? null}

        <div
                n:if="$leaflet"
            class="flex flex-col lg:flex-row items-center gap-4 lg:gap-8 p-2 rounded-xl border border-[#EEE] mb-10"
            style="rgba(30, 168, 101, 0.02)"
        >
            {var $shop = $leaflet->getShop()}
            {var $secondPage = $leaflet->getPageByNumber(2)}
            <div class="flex justify-center lg:justify-normal relative w-full lg:w-[544px]">
                <div class="absolute inset-0"></div>
                <div>
                    <img class="md:min-w-[272px] border rounded-l-lg h-auto" src="{$leaflet->getFirstPage()->getImageUrl() |image:272,336,'exactTop','webp'}" alt="">
                </div>
                <div n:if="$secondPage">
                    <img class="md:min-w-[272px] border rounded-r-lg h-auto" src=" {$secondPage->getImageUrl() |image:242,336,'exactTop'}" alt="">
                </div>
            </div>

            <div class="flex flex-col justify-between px-5 pb-5 lg:px-0">
                <div class="mb-5 md:mb-10">
                    <div class="flex items-center gap-[9px] mb-[17px]">
                        <img class="hidden md:block w-[36px] h-[36px] rounded" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="letak">

                        <div class="flex flex-col">
                            <div class="text-lg">{$shop->getName()} letáky</div>
                            <div class="text-xs font-light">{$leaflet->getValidSince() |localDate: 'short'} - {$leaflet->getValidTill() |localDate: 'long'}</div>
                        </div>
                    </div>

                    <div class="flex gap-2 mb-[23px]">
                        <div class="text-sm text-white font-medium leading-[24.5px] py-1 px-2.5 bg-primary rounded-md">{_oferto.leaflet.current}</div>
                        <div class="text-sm font-medium leading-[24.5px] py-1 px-2.5 border border-[#DEDEDE] rounded-md">{_oferto.leaflet.validTilLDays, ['count' => $leaflet->getValidTillDays()]}</div>
                    </div>

                    <div class="text-sm md:text-base text-grey-description font-light leading-7">
                        Lidl leták od 14. 11. nabízí slevy na potraviny, sezónní zboží a vybavení do domácnosti. Najdete zde také výhodné nabídky na kuchyňské spotřebiče a zimní doplňky.
                    </div>
                </div>

                <div class="mt-auto">
                    <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="flex justify-center items-center text-primary gap-[9px] py-[17px] w-full lg:max-w-[325px] border border-primary rounded-xl hover:bg-primary-light-hover transition duration-200">
                        {_oferto.leaflet.openLeaflet}
                        <svg xmlns="http://www.w3.org/2000/svg" width="17" height="18" viewBox="0 0 17 18" fill="none">
                            <path d="M3.8365 13.6033L12.2764 5.16345" stroke="currentColor" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M13.0443 12.0688L13.0377 4.38964L5.38502 4.38314" stroke="currentColor" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>

        {* TODO - druhy a dalsi letaky *}
        <div class="mt-3 grid grid-cols-2 md:grid-cols-5 gap-3 mb-8">
            {foreach $leaflets as $leaflet}                
                {breakIf $leaflet->isExpired() && $iterator->getCounter() > 5}
                
                {include '../components/leaflet.latte', leaflet => $leaflet}                
                                        
                {if $iterator->getCounter() == 2}        
                    {if $channel == 'c1' && $localization->isCzech()}
                        <!-- Medium rectangle1 -->
                        <div id="protag-medium_rectangle_1"></div>
                        <script type="text/javascript">
                        window.googletag = window.googletag || { cmd: [] };
                        window.protag = window.protag || { cmd: [] };
                        window.protag.cmd.push(function () {
                            window.protag.display("protag-medium_rectangle_1");
                        });
                        </script>
                    {else}                        
                        <div n:if="$website->hasAdSense()" class="block md:hidden">
                            <!-- oferto.com / mobile_rectangle2 -->
                            {include "../components/mobile_rectangle2.latte"}
                        </div>                                
                    {/if}
                {/if}
            {/foreach}     
        </div>                                                    

        {if $expiredLeaflets}
            <p class="flex">
                <a n:href="Archive:archive, $shop" class="link ml-auto">{_"$websiteType.leaflets.expiredMetaTitle", [brand => $shop->getName()]} »</a>
            </p>
        {/if}        
    {/if}                                                          
    
    {if $channel == 'c1' && $localization->isCzech()}
        <!-- Leaderboard -->
        <div id="protag-leaderboard"></div>
        <script type="text/javascript">
        window.googletag = window.googletag || { cmd: [] };
        window.protag = window.protag || { cmd: [] };
        window.protag.cmd.push(function () {
            window.protag.display("protag-leaderboard");
        });
        </script>

    {else}
        <!-- oferto.com / pr_native1 -->
        {include "../components/pr_native1.latte"}                

        <!-- oferto.com / pr_native3 -->
        {include "../components/pr_native3.latte"}                                
    {/if}
    
    {if $contentBlocksAllowed}
        {foreach $contentBlocks as $contentBlock}
            {continueIf $contentBlock->getType() === 'legacy'}

            <div class="k-content k__text mw-900 mb-5 ml-0" n:if="$contentBlock->getContent()">
                <h2 n:if="$contentBlock->getheading()">{$contentBlock->getHeading()}</h2>

                {$contentBlock->getContent() |content|noescape}
            </div>
        {/foreach}
    {else}
        <p class="mw-900 mb-6">
            {if $pageExtension && $pageExtension->getShortDescription()}
                {$pageExtension->getShortDescription()}
            {else}
                {_"$websiteType.shop.text", [brand => $shop->getName()]|noescape}
            {/if}
        </p>

        {if $pageExtension && $pageExtension->getLongDescription()}
            <div class="mw-900 mb-6">
                {$pageExtension->getLongDescription()|content|noescape}
            </div>
        {/if}
    {/if}     
</div>           

<div class="bg-light-6">
    <div class="container py-10">
        <div n:if="count($similarShops)-1 > 0" class="mb-[28px] md:mb-[37px]">
            <div class="flex relative justify-between md:gap-[5px] leading-[35px] md:leading-[39px] text-[20px] md:text-[26px] font-medium mb-4 ">
                <div>
                    <h2 class="flex items-center">
                        {_"$websiteType.shop.otherShops.title"}
                    </h2>

                    <p class="max-w-[900px] text-sm font-light leading-[22px] text-[#646C7C]">{_"$websiteType.shop.otherShops.text", [brand => $shop->getName(), actualShops => $actualShops, actualActiveLeaflets => $actualActiveLeaflets]|noescape}</p>
                </div>

                <div class="hidden md:flex gap-[60px] flex-shrink-0 w-20">
                    <div class="swiper-button-prev favorite-shops"></div>
                    <div class="swiper-button-next favorite-shops"></div>
                </div>
            </div>

            <div class="swiper favorite-shops -mr-5 md:mr-0">
                <div class="swiper-wrapper">
                    {foreach $similarShops as $similarShop}
                        {continueIf $similarShop->getId() == $shop->getId()}
                        <a n:href="Shop:shop $similarShop" class="swiper-slide">
                            <span class="w-36 h-36 flex items-center justify-center rounded-lg bg-white">
                                <picture>
                                    <source srcset="{$similarShop->getLogoUrl() |image:120,120,'fit','webp'}" type="image/webp">
                                    <img src="{$similarShop->getLogoUrl() |image:120,120}" width="120" height="120" alt="{$similarShop->getName()}" loading="lazy">
                                </picture>                                    
                            </span>                            
                        </a>
                    {/foreach}                    
                </div>
            </div>
        </div>

        <div class="hidden md:block w-full h-px bg-light-2 my-[50px]"></div>
    </div>
</div>

            
<div n:if="$articles" class="container">        
    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_"$websiteType.shop.otherArticles"}</h2>
    {include "../components/article-list.latte", articles => $articles}    
</div>

<script type="application/ld+json" n:if="count($leaflets)">
    {
        "@context": "http://schema.org",
        "itemListElement": [
            {foreach $leaflets as $leaflet}
                {
                    "endDate": {$leaflet->getValidTill()->format('Y-m-d')},
                    "startDate": {$leaflet->getValidSince()->format('Y-m-d')},
                    "location": {
                        "address": {
                            "name": {$leaflet->getName()},
                            "@type": "PostalAddress"
                        },
                        "url": {link //:Oferto:Shop:shop $leaflet->getShop()},
                        "image": {$shop->getLogoUrl() |image:160,140,'fit','webp'},
                        "name": {$leaflet->getShop()->getName()},
                        "@type": "Place"
                    },
                    "performer": {
                        "name": {$leaflet->getShop()->getName()},
                        "@type": "Organization"
                    },
                    "image": {$leaflet->getFirstPage() ? $leaflet->getFirstPage()->getImageUrl() |image:60,84,'exactTop','webp' : ""},
                    "name": {$leaflet->getName()},
                    "url": {link //:Oferto:Leaflet:leaflet, $leaflet->getShop(), $leaflet},
                    "description": "",
                    "eventAttendanceMode": "https://schema.org/OfflineEventAttendanceMode",
                    "eventStatus": "https://schema.org/EventScheduled",
                    "organizer": {
                        "@type": "Organization",
                        "name": {$leaflet->getShop()->getName()},
                        "url": {link //:Oferto:Shop:shop $leaflet->getShop()}
                    },
                    "@type": "SaleEvent"
                }{sep},{/sep}
    {/foreach}
        ],
        "@type": "OfferCatalog"
    }
</script>

<script type="module">
    import Swiper from 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.mjs'

    const FavoriteSwiper = new Swiper('.swiper.favorite-shops', {
        direction: 'horizontal',
        slidesPerView: 3.5,
        breakpoints: {
            1024: {
                slidesPerView: 7,
                spaceBetween: 36,
            },
            768: {
                slidesPerView: 5,
                spaceBetween: 36,
            }
        },
        loop: false,
        navigation: {
            nextEl: '.swiper-button-next.favorite-shops',
            prevEl: '.swiper-button-prev.favorite-shops',
        },
    });

    const LeafletSwiper = new Swiper('.swiper.leaflet', {
        direction: 'horizontal',
        slidesPerView: 3.2,
        spaceBetween: 12,
        breakpoints: {
            1024: {
                slidesPerView: 8.3,
                spaceBetween: 12,
            },
            768: {
                slidesPerView: 5,
                spaceBetween: 12,
            }
        },
        loop: false,
        navigation: {
            nextEl: '.swiper-button-next.leaflet',
            prevEl: '.swiper-button-prev.leaflet',
        },
    });
</script>

<style>
    .swiper.favorite-shops,
    .swiper-button-prev.favorite-shops,
    .swiper-button-next.favorite-shops {
        position: relative;
        color: #292D32;
    }

    .swiper-button-prev.favorite-shops.swiper-button-disabled::after {
        font-size: 30px;
    }
    .swiper-button-next.favorite-shops.swiper-button-disabled::after {
        font-size: 30px;
    }

    /* LEAFLET SWIPER */
    .swiper-button-prev.leaflet,
    .swiper-button-next.leaflet {
        color: white;
        background: black;
        border-radius: 4px;
        width: 32px;
        height: 32px;
        top: 44%;
        right: -30px;
    }
    .swiper-button-prev.leaflet {
        left: -13px;
    }


    .swiper-button-prev.leaflet::after,
    .swiper-button-next.leaflet::after {
        font-size: 14px;
        font-weight: 700;
    }

    .swiper-button-prev.leaflet.swiper-button-disabled,
    .swiper-button-next.leaflet.swiper-button-disabled {
        display: none;
    }

    @media (max-width: 768px) {
        .swiper-button-prev.leaflet,
        .swiper-button-next.leaflet {
            display: none;
        }
    }
</style>
