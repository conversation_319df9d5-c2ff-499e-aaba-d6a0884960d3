@use "tools";
@use "vars";

.k-coupon-expire {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    border-bottom: 1px solid #D9D9D9;
    padding: 1rem;    
}

.k-coupon-expire svg {
    display: block;
    width: 30px;
    height: 30px;
    margin-right: 1rem;
}

.k-coupon-expire small {
    margin-right: 1rem;
}


.k-coupon-expire-wrapper {
    max-height: 200px;
	overflow: hidden;
	transition: max-height 0.2s ease-out;	
}

.k-coupon-expire__show {
	position: absolute;
	display: flex;
	width: 100%;
	height: 76px;
	left: 0;
	bottom: 0;
	align-items: flex-end;
	justify-content: flex-start;
	background: linear-gradient(rgba(255, 255, 255, 0.1) 0%, #fff 60%);
	cursor: pointer;

    @include tools.breakpoint(vars.$tablet) {		
        font-size: 16px;
    }
}

.k-coupon-expire__show-inner {
	display: flex;
	align-items: center;
}

.k-coupon-expire__show-inner:hover {
	@include tools.breakpoint(vars.$desktop) {
		text-decoration: underline;
	}
}

.k-coupon-expire__show-icon {
	margin-left: 8px;
}

// Content show content btn
.k-coupon-expire__content-btn {
	display: none;
}

.k-coupon-expire__content-btn:checked ~ .k-coupon-expire-wrapper {
	max-height: 600px;
}

.k-coupon-expire__content-btn:checked ~ .k-coupon-expire-wrapper .k-coupon-expire__show {
	display: none;
}