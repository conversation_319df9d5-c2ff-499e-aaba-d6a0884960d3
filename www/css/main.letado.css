* {
  box-sizing: border-box;
}

*:before,
*:after {
  box-sizing: border-box;
}

html {
  font-size: 10px;
}

body {
  font-family: Nunito, Helvetica Neue, Helvetica, Arial, sans-serif;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  margin: 0;
  background-color: #fff;
  padding: 0px !important;
}

p {
  margin: 0;
  line-height: 1.7;
}

a {
  color: #000;
  text-decoration: none;
}

.link {
  color: #2c2c2c;
  background-color: transparent;
  border: 0;
  cursor: pointer;
}

.link:hover {
  text-decoration: underline;
}

button.link {
  font-size: 14px;
  line-height: 1.25;
}

.container {
  position: relative;
  display: block;
  width: calc(100% - 30px);
  margin: auto;
  padding: 0 1rem;
}
@media (min-width: 1180px) {
  .container {
    width: 1170px;
    padding: 0 3rem;
  }
}

.container--flex {
  display: flex;
  align-items: center;
  padding: 0 1rem;
}

.p-relative {
  position: relative;
}

.d-none {
  display: none;
}

.d-block {
  display: block;
}

@media (min-width: 768px) {
  .d-sm-block {
    display: block;
  }
}

.d-inline-block {
  display: inline-block;
}

.d-flex {
  display: flex;
}

@media (min-width: 768px) {
  .d-sm-flex {
    display: flex;
  }
}

@media (min-width: 1024px) {
  .d-md-flex {
    display: flex;
  }
}

.align-items-center {
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.flex-grow-1 {
  flex-grow: 1;
}

.justify-content-center {
  justify-content: center;
}

@media (min-width: 768px) {
  .justify-content-sm-start {
    justify-content: flex-start;
  }
}

.flex-wrap-no-wrap {
  flex-wrap: nowrap;
}

.flex-wrap {
  flex-wrap: wrap;
}

@media (min-width: 768px) {
  .flex-wrap-sm-no-wrap {
    flex-wrap: nowrap;
  }
}

.flex-direction-row {
  flex-direction: row;
}

.flex-direction-column {
  flex-direction: column;
}

@media (min-width: 768px) {
  .flex-direction-sm-row {
    flex-direction: row;
  }
}

@media (min-width: 1024px) {
  .flex-direction-md-column {
    flex-direction: column;
  }
}

@media (min-width: 1200px) {
  .flex-direction-lg-row {
    flex-direction: row;
  }
}

@media (min-width: 1200px) {
  .flex-direction-lg-column {
    flex-direction: column;
  }
}

.w100 {
  width: 100%;
}

.h100 {
  height: 100%;
}

@media (min-width: 768px) {
  .w-sm-auto {
    width: auto;
  }
}

.pb-0 {
  padding-bottom: 0;
}

.pb-1 {
  padding-bottom: 0.25rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-3 {
  padding-bottom: 1rem;
}

.pb-4 {
  padding-bottom: 1.5rem;
}

.pb-5 {
  padding-bottom: 3rem;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.px-3 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-3 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.px-4 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.py-4 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.px-5 {
  padding-left: 3rem;
  padding-right: 3rem;
}

.py-5 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

@media (min-width: 1200px) {
  .px-lg-0 {
    padding-left: 0rem;
    padding-right: 0rem;
  }
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-3 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.m-auto {
  margin: auto;
}

.mt-0 {
  margin-top: 0;
}

.mt-3 {
  margin-top: 1rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.mt-5 {
  margin-top: 3rem;
}

@media (min-width: 768px) {
  .mt-sm-0 {
    margin-top: 0rem;
  }
}

@media (min-width: 768px) {
  .mt-sm-5 {
    margin-top: 3rem;
  }
}

@media (min-width: 1024px) {
  .mt-md-0 {
    margin-top: 0rem;
  }
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mb-5 {
  margin-bottom: 3rem;
}

.mb-6 {
  margin-bottom: 4.5rem;
}

.mb-7 {
  margin-bottom: 6rem;
}

@media (min-width: 768px) {
  .mb-sm-0 {
    margin-bottom: 0;
  }
}

@media (min-width: 768px) {
  .mb-sm-3 {
    margin-bottom: 1rem;
  }
}

.ml-auto {
  margin-left: auto;
}

@media (min-width: 768px) {
  .ml-sm-auto {
    margin-left: auto;
  }
}

@media (min-width: 1024px) {
  .ml-md-auto {
    margin-left: auto;
  }
}

.ml-0 {
  margin-left: 0;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 1rem;
}

.ml-4 {
  margin-left: 1.5rem;
}

.ml-5 {
  margin-left: 3rem;
}

.mr-auto {
  margin-right: auto;
}

.mr-0 {
  margin-right: 0;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 1rem;
}

.mr-4 {
  margin-right: 1.5rem;
}

.mr-5 {
  margin-right: 3rem;
}

@media (min-width: 768px) {
  .mr-sm-2 {
    margin-right: 0.5rem;
  }
}

@media (min-width: 768px) {
  .ml-sm-3 {
    margin-left: 1rem;
  }
}

@media (min-width: 1200px) {
  .mb-lg-0 {
    margin-right: 0rem;
  }
}

@media (min-width: 1200px) {
  .mb-lg-3 {
    margin-right: 1rem;
  }
}

@media (min-width: 1200px) {
  .mr-lg-3 {
    margin-right: 1rem;
  }
}

@media (min-width: 768px) {
  .pl-sm-3 {
    padding-left: 1rem;
  }
}

@media (min-width: 768px) {
  .pl-sm-4 {
    padding-left: 1.5rem;
  }
}

@media (min-width: 768px) {
  .pr-sm-3 {
    padding-right: 1rem;
  }
}

.overflow-hidden {
  overflow: hidden;
}

.img-responsive {
  display: block;
  max-width: 100%;
  height: auto;
  margin: auto;
}

.ellipsis {
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.fw-regular {
  font-weight: 400;
}

.fw-semibold {
  font-weight: 600;
}

.fw-bold,
.strong {
  font-weight: 700;
}

.tt-uppercase {
  text-transform: uppercase;
}

.td-underline {
  text-decoration: underline;
}

.td-none {
  text-decoration: none;
}

.td-hover-none:hover {
  text-decoration: none;
}

.td-hover-underline:hover {
  text-decoration: underline;
}

.ta-center {
  text-align: center;
}

@media (min-width: 768px) {
  .sm-ta-left {
    text-align: left;
  }
}

.mw-700 {
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.mw-700.ml-0 {
  margin-left: 0;
}

.min-w-300 {
  min-width: 300px;
}

.mw-400 {
  max-width: 400px;
}

.mw-805 {
  max-width: 805px;
}

small {
  font-size: inherit;
}

.fz-xxs {
  font-size: 11px;
}

.fz-s {
  font-size: 12px;
}

.fz-m {
  font-size: 14px;
}

.fz-l {
  font-size: 16px;
}

.fz-xl {
  font-size: 18px;
}

.fz-xxl {
  font-size: 20px;
}

@media (min-width: 768px) {
  .fz-sm-xs {
    font-size: 12px;
  }
}

.color-grey {
  color: #2c2c2c;
}

.color-grey a {
  color: #2c2c2c;
}

.color-light-grey {
  color: #929292;
}

.color-black {
  color: #000;
}

.lh-15 {
  line-height: 1.5;
}

.fw-700 {
  font-weight: 700;
}

.row {
  position: relative;
  display: flex;
  flex-wrap: wrap;
}

.col-6 {
  width: 50%;
  flex: 0 0 50%;
}

.col-12 {
  width: 100%;
  flex: 0 0 100%;
}

@media (min-width: 768px) {
  .col-sm-3 {
    width: 25%;
    flex: 0 0 25%;
  }
}

@media (min-width: 768px) {
  .col-sm-6 {
    width: 50%;
    flex: 0 0 50%;
  }
}

@media (min-width: 768px) {
  .col-sm-9 {
    width: 75%;
    flex: 0 0 75%;
  }
}

@media (min-width: 1180px) {
  .col-md-6 {
    width: 50%;
    flex: 0 0 50%;
  }
}

@media (min-width: 768px) {
  .sm-column-count-2 {
    -moz-column-count: 2;
         column-count: 2;
  }
}

.k-offers__item.hidden,
.k-shop__item.hidden,
.k-tag__inner.hidden,
.k-leaflets__item.hidden {
  display: none;
}

.k-show-more-button {
  font-size: 14px;
  padding: 1rem;
  border: 1px solid #b3b3b3;
  border-radius: 4px;
  margin-right: auto;
  margin-left: auto;
}

.k-show-more-button.hidden {
  display: none;
}

.align-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.space-between {
  justify-content: space-between;
}

.underline {
  text-decoration: underline;
}

.text-center {
  text-align: center;
}

.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-grayscale);
}

.contest-form form {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.contest-form input {
  border: 1px solid #B3B3B3;
  padding: 1rem;
  border-radius: 10px;
  margin: 0.5rem 0;
  margin-right: 0.5rem;
  font-size: 16px;
  min-width: 270px;
}

.contest-form input[type=submit] {
  background-color: #009e74;
  color: #fff;
  font-weight: bold;
  min-width: 100px;
  cursor: pointer;
}

.contest-box {
  max-width: 720px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1490196078);
  margin: auto;
  padding: 1rem 2rem;
  margin-bottom: 3rem;
}

.contest-box h2 {
  font-size: 20px;
}

.contest-promo {
  position: fixed;
  display: flex;
  align-items: center;
  width: 100%;
  bottom: 0px;
  background-color: #009e74;
  padding: 1rem 2rem;
}
@media (min-width: 768px) {
  .contest-promo {
    width: auto;
    bottom: 30px;
    right: 30px;
    border-radius: 20px;
    padding: 1.5rem 2rem;
    box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.4);
  }
}

.contest-promo__medal {
  width: 33px;
  height: 44px;
  margin-right: 1rem;
}

.contest-promo span {
  font-size: 18px;
  line-height: 22px;
  text-align: left;
  color: #fff;
  margin-right: 1rem;
}

.contest-promo strong {
  display: block;
  font-weight: 700;
}

.contest-promo__cross {
  margin-left: auto;
  cursor: pointer;
}

.hidden {
  display: none;
}

.kf-click-out {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.15s;
  color: #fff;
}

.kf-click-out:hover {
  opacity: 1;
}

.kf-click-out:hover svg {
  cursor: pointer;
  transform: scale(1.25);
}

.kf-click-out a {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kf-click-out svg {
  background: #009e74;
  border-radius: 50%;
  box-sizing: border-box;
  fill: #fff;
  width: 1.75em;
  height: 1.75em;
  padding: 0.25em;
  pointer-events: none;
  transition: transform 0.3s cubic-bezier(0.5, -0.15, 0.7, 4);
}

.montagu-slab {
  font-family: "Montagu Slab", serif;
  font-optical-sizing: auto;
  font-weight: 700;
  font-style: normal;
  color: #009e74;
}

.alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px;
}

.alert h4 {
  margin-top: 0;
  color: inherit;
}

.alert .alert-link {
  font-weight: bold;
}

.alert > p,
.alert > ul {
  margin-bottom: 0;
}

.alert > p + p {
  margin-top: 5px;
}

.alert-dismissable,
.alert-dismissible {
  padding-right: 35px;
}

.alert-dismissable .close,
.alert-dismissible .close {
  position: relative;
  top: -2px;
  right: -21px;
  color: inherit;
}

.alert-success {
  color: #3c763d;
  background-color: #dff0d8;
  border-color: #d6e9c6;
}

.alert-success hr {
  border-top-color: #c9e2b3;
}

.alert-success .alert-link {
  color: #2b542c;
}

.alert-info {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1;
}

.alert-info hr {
  border-top-color: #a6e1ec;
}

.alert-info .alert-link {
  color: #245269;
}

.alert-warning {
  color: #7b6d4c;
  background-color: #fff9d4;
  border-color: #faebcc;
}

.alert-warning hr {
  border-top-color: #f7e1b5;
}

.alert-warning .alert-link {
  color: #66512c;
}

.alert-danger {
  color: #a94442;
  background-color: #f2dede;
  border-color: #ebccd1;
}

.alert-danger hr {
  border-top-color: #e4b9c0;
}

.alert-danger .alert-link {
  color: #843534;
}

.alert__icon {
  position: absolute;
  left: 18px;
  top: 50%;
  margin-top: -12px;
}

.k__title {
  font-size: 22px;
  font-weight: 600;
  line-height: 1.5;
  color: #000;
}
@media (min-width: 768px) {
  .k__title {
    font-size: 24px;
  }
}

.k__title.fw-700 {
  font-weight: 700;
}

.k__title a {
  color: #000;
}

.k__title a:hover {
  text-decoration: underline;
}

.k__text,
.k__text p {
  font-size: 12px;
  line-height: 1.5;
  color: #2c2c2c;
}
@media (min-width: 1180px) {
  .k__text,
  .k__text p {
    font-size: 14px;
  }
}

.k__text a {
  text-decoration: underline;
}

.k__text a:hover {
  text-decoration: none;
}

.ot-sdk-show-settings {
  background: none;
  border: 0;
  padding: 0;
  color: #2c2c2c;
  cursor: pointer;
}

.ot-sdk-show-settings:hover {
  text-decoration: underline;
}

.k-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #009e74;
  border-radius: 40px;
  font-weight: bold;
  font-size: 12px;
  line-height: 14px;
  padding: 1rem;
  color: #fff;
  background-color: #009e74;
  cursor: pointer;
}
@media (min-width: 768px) {
  .k-button {
    min-width: 220px;
  }
}

.k-button:hover {
  background-color: rgb(0, 107, 78.5569620253);
}

.k-button.hide {
  display: none;
}

.author {
  font-size: 30px;
}
.author .author__container {
  padding: 48px 12px;
  width: 1170px;
  max-width: 100%;
  margin: auto;
}
.author .author__content {
  display: flex;
  align-items: center;
  gap: 40px;
}
.author .author__content .author__avatar {
  border-radius: 50%;
  max-width: 160px;
  height: auto;
}
.author .author__content .author__title {
  font-size: 72px;
  font-weight: 600;
}
.author .author__content .author__articles-count {
  font-size: 14px;
  font-weight: 400;
}

.k-hp-swiper {
  padding: 12px;
}
.k-hp-swiper .swiper-button-disabled {
  display: none;
}
.k-hp-swiper .swiper-button-prev {
  left: 1px;
}
.k-hp-swiper .swiper-button-next {
  right: 6px;
}
.k-hp-swiper .swiper-button-next, .k-hp-swiper .swiper-button-prev {
  border-radius: 50%;
  background-color: white;
  padding: 21px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  transition: box-shadow 0.2s ease;
  color: #bc2026;
}
.k-hp-swiper .swiper-button-next:hover, .k-hp-swiper .swiper-button-prev:hover {
  box-shadow: 0 1px 6px #bc2026;
}
.k-hp-swiper .swiper-button-next::after, .k-hp-swiper .swiper-button-prev::after {
  font-weight: bold;
  font-size: 18px;
}
.k-hp-swiper .img-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  width: 90px;
  height: 90px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  transition: box-shadow 0.2s ease;
  border: 2px solid #009e74;
  overflow: hidden;
}
.k-hp-swiper .img-wrapper:hover {
  cursor: pointer;
  box-shadow: 0 1px 6px #bc2026;
}
.k-hp-swiper .swiper-slide {
  width: auto;
  opacity: 0;
}
.k-hp-swiper .swiper-slide img {
  max-width: 40px;
  height: auto;
}
@media (min-width: 768px) {
  .k-hp-swiper .swiper-slide img {
    max-width: 50px;
  }
}

.swiper-initialized .swiper-slide {
  opacity: 1;
}

@media only screen and (max-width: 500px) {
  .k-hp-swiper {
    padding: 12px 0px 12px 4px;
  }
  .k-hp-swiper .img-wrapper {
    width: 64px;
    height: 64px;
  }
  .k-hp-swiper .swiper-button-next, .k-hp-swiper .swiper-button-prev {
    display: none;
  }
}
.faq-answer {
  display: none;
}

.faq-answer.show {
  display: block;
}

.faq-section {
  margin-top: 36px;
}
.faq-section .faq-section-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 12px;
}
.faq-section .faq-question {
  padding: 16px 12px;
  cursor: pointer;
  border-radius: 6px;
  box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.2);
  margin-bottom: 16px;
}
.faq-section .faq-question .faq-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}
.faq-section .faq-question .faq-answer {
  margin-top: 16px;
  font-weight: 400;
  color: #000;
}
.faq-section .faq-question .arrow {
  margin-left: 32px;
  transition: transform 0.3s, fill 0.3s;
  fill: #bc2026;
}
.faq-section .faq-question .rotate {
  transform: rotate(180deg);
  fill: #bc2026;
}
.faq-section .faq-question.show {
  border-radius: 6px;
  color: #000;
  background-color: rgba(255, 255, 255, 0.04);
  border-bottom: none;
}

.faq-answer a {
  text-decoration: underline;
}

.faq-answer a:hover {
  text-decoration: none;
}

.faq-section.mt-3 {
  margin-top: 1rem;
}

@media only screen and (max-width: 768px) {
  .author .author__content {
    gap: 20px;
  }
  .author .author__content .author__avatar {
    width: 80px;
  }
  .author .author__content .author__title {
    font-size: 48px;
  }
}
@media only screen and (max-width: 360px) {
  .author .author__content {
    flex-direction: column;
    text-align: center;
  }
  .author .author__content .author__avatar {
    width: 130px;
  }
}
.ads-container {
  position: relative;
  padding-top: 20px;
  text-align: center;
  padding-bottom: 25px;
  min-height: 300px;
}

.ads-container--mobile {
  display: block;
  min-height: 200px;
}
@media (min-width: 1180px) {
  .ads-container--mobile {
    display: none;
  }
}

.ads-container--desktop {
  display: none;
}
@media (min-width: 1180px) {
  .ads-container--desktop {
    display: block;
    min-height: 90px;
  }
}

.ads-container--branding {
  min-height: 0px;
  margin: 0;
  padding: 0;
}
@media (min-width: 1180px) {
  .ads-container--branding {
    min-height: 250px;
  }
}

.ads-container--branding .ads-label {
  display: none;
}

.us-wrapper {
  position: relative;
  padding-top: 20px;
  text-align: center;
  padding-bottom: 25px;
}

.us-wrapper--130 {
  height: 130px;
}

.ads-label {
  position: absolute;
  padding: 0 8px;
  width: 100%;
  color: #888;
  right: 0;
  top: 2px;
  font-size: 10px;
  line-height: 16px;
}

.order-first {
  order: -9999;
}

@media (min-width: 768px) {
  .order-sm-1 {
    order: 1;
  }
}

.nice-select {
  display: flex;
  align-items: center;
  background-color: transparent;
}
@media (min-width: 768px) {
  .nice-select {
    margin-right: 1rem;
  }
}

.nice-select .current {
  display: inline-block;
  background: #009e74;
  padding: 0.25rem 0.5rem;
  border-radius: 8px;
  color: #fff;
  font-weight: bold;
  line-height: 1.5;
  font-size: 12px;
}

.nice-select__button {
  background: transparent;
  border: 1px solid #009e74;
  border-radius: 20px;
  margin-right: 4px;
  height: 32px;
  display: block;
  cursor: pointer;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 12px;
  color: #009e74;
}

.k-header {
  position: relative;
  display: flex;
  align-items: center;
  height: 50px;
}
@media (min-width: 1180px) {
  .k-header {
    height: 80px;
  }
}

.k-header .container {
  padding: 0;
}

.k-header__logo-link {
  position: relative;
  display: block;
  width: 90px;
  flex: 0 0 90px;
}
@media (min-width: 375px) {
  .k-header__logo-link {
    width: 100px;
    flex: 0 0 100px;
  }
}
@media (min-width: 1180px) {
  .k-header__logo-link {
    width: 186px;
    flex: 0 0 186px;
  }
}

.k-header__logo-link--flex {
  display: flex;
  align-items: center;
}

.k-header__logo-link__logo {
  flex-shrink: 0;
  width: 26px;
  height: auto;
}
@media (min-width: 1180px) {
  .k-header__logo-link__logo {
    width: 40px;
  }
}

.k-header__logo-text {
  margin-left: 0.5rem;
  font-size: 18px;
  flex-shrink: 0;
  letter-spacing: -0.8px;
}
@media (min-width: 1180px) {
  .k-header__logo-text {
    font-size: 32px;
  }
}

.k-header__logo-link img {
  position: relative;
  display: block;
  max-width: 100%;
  height: auto;
}

.k-header__search {
  position: relative;
  display: flex;
  align-items: center;
  width: 200px;
  height: 40px;
  margin-left: auto;
  border: 1px solid #d9d9d9;
  border-radius: 40px;
}
@media (min-width: 768px) {
  .k-header__search {
    width: 270px;
  }
}

.k-header__search-input {
  position: relative;
  display: block;
  width: 100px;
  flex: 1 1 auto;
  height: 38px;
  margin-left: 1px;
  border-radius: 40px;
  padding-left: 1rem;
  border: 0;
  outline: none;
  line-height: 16px;
  color: #2c2c2c;
  font-size: 14px;
}
@media (min-width: 768px) {
  .k-header__search-input {
    padding-left: 2rem;
  }
}

.k-header__search-submit {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto;
  min-width: 67px;
  height: 32px;
  margin-right: 4px;
  background: #fff;
  border: 1px solid #009e74;
  border-radius: 40px;
  font-weight: bold;
  color: #009e74;
  font-size: 12px;
  line-height: 14px;
  text-transform: uppercase;
  cursor: pointer;
  -webkit-appearance: none;
}
@media (min-width: 768px) {
  .k-header__search-submit {
    min-width: 87px;
  }
}

.k-header__search-submit:hover {
  background-color: #009e74;
  color: #fff;
}

.k-header__search-wrapper {
  position: absolute;
  display: none;
  width: 100%;
  left: 0;
  top: 44px;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  z-index: 99;
}

.k-header__search-wrapper.show {
  display: block;
}

.k-header__nav {
  position: absolute;
  display: none;
  align-items: center;
  flex-direction: column;
  background: #fff;
  width: 100%;
  top: 46px;
  z-index: 9;
  left: 0;
}
@media (min-width: 1024px) {
  .k-header__nav {
    position: relative;
    display: flex;
    width: auto;
    margin-left: auto;
    flex-direction: row;
    top: 0;
  }
}

.k-header__nav.show {
  display: flex;
}

.k-header__nav-item {
  display: block;
  flex: 0 0 auto;
  width: 100%;
  color: #000;
  font-size: 16px;
  line-height: 19px;
  padding: 15px;
  text-align: center;
}
@media (min-width: 1024px) {
  .k-header__nav-item {
    width: auto;
    text-align: left;
  }
}

.k-header__nav-item:hover {
  color: #009e74;
  text-decoration: underline;
}

.k-header__nav-item.color-grey {
  color: #2c2c2c;
}

.k-header__nav-dropdown-wrapper {
  position: relative;
  width: 100%;
}

.k-header__nav-dropdown-wrapper:hover .k-header__nav-dropdown {
  display: block;
}

.k-header__nav-dropdown {
  position: relative;
  display: block;
}
@media (min-width: 1024px) {
  .k-header__nav-dropdown {
    position: absolute;
    display: none;
    min-width: 110px;
    height: auto;
    top: 44px;
    left: 15px;
    border: 1px solid #d9d9d9;
    background-color: #fff;
    z-index: 99;
  }
}

.k-header__nav-dropdown-item {
  position: relative;
  display: block;
  width: 100%;
  color: #2c2c2c;
  font-size: 16px;
  line-height: 19px;
  padding: 15px;
  text-align: center;
}
@media (min-width: 1024px) {
  .k-header__nav-dropdown-item {
    color: #2c2c2c;
    font-size: 14px;
    text-align: left;
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #d9d9d9;
  }
}

.k-header__nav-dropdown-item:hover {
  background-color: #d9d9d9;
}

.k-header__nav-dropdown-item:last-child {
  border-bottom: 0;
}

.k-header__menu-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0;
  outline: none;
  background: none;
  padding: 0;
  margin-left: 1rem;
  outline: none;
  -webkit-appearance: none;
}
@media (min-width: 375px) {
  .k-header__menu-icon {
    margin-left: 1rem;
  }
}
@media (min-width: 1024px) {
  .k-header__menu-icon {
    display: none;
  }
}

.k-header__menu-icon svg {
  position: relative;
  display: block;
  width: 20px;
  height: 20px;
  outline: none;
  -webkit-appearance: none;
  color: #009e74;
}

.k-header__nav-separator {
  display: none;
}
@media (min-width: 1024px) {
  .k-header__nav-separator {
    display: block;
  }
}

.k-header .google-auto-placed {
  max-height: 50px;
  overflow: hidden;
}
@media (min-width: 1180px) {
  .k-header .google-auto-placed {
    max-height: 80px;
  }
}

.k-header__category {
  display: none;
  overflow-y: auto;
}
@media (min-width: 1180px) {
  .k-header__category {
    display: block;
  }
}
@media (min-width: 1200px) {
  .k-header__category {
    overflow-y: hidden;
  }
}

.k-header__category .container {
  justify-content: start;
}
@media (min-width: 1200px) {
  .k-header__category .container {
    justify-content: center;
    padding-bottom: 2rem;
  }
}

.k-header__category-item {
  flex: 1 0 auto;
  padding: 1rem 0;
}

.k-header--city-picker {
  height: auto;
}

.k-header--city-picker .container {
  flex-wrap: wrap;
  padding: 1rem 0;
}
@media (min-width: 768px) {
  .k-header--city-picker .container {
    padding: 1rem;
  }
}

.k-header--city-picker .k-header__menu-icon {
  margin-left: auto;
}

.k-header__search--city-picker {
  width: 100%;
  margin-top: 1.5rem;
  margin-left: auto;
  margin-right: auto;
  order: 1;
}
@media (min-width: 768px) {
  .k-header__search--city-picker {
    width: auto;
    order: 0;
    margin-top: 0;
    margin-right: 0;
  }
}

.k-header__search--city-picker form {
  display: flex;
  width: 100%;
  align-items: center;
}

.k-header__search--city-picker input {
  width: 110px;
  padding-left: 0;
  flex-grow: 1;
}
@media (min-width: 768px) {
  .k-header__search--city-picker input {
    width: 130px;
    padding-left: 10px;
  }
}

.k-header__search--city-picker input.k-header__search-submit {
  width: 68px;
  padding: 0;
}
@media (min-width: 768px) {
  .k-header__search--city-picker input.k-header__search-submit {
    width: 88px;
  }
}

.k-header__search--city-picker select {
  border: 0;
  max-width: 68px;
  flex-grow: 1;
  margin-right: 1rem;
}
@media (min-width: 768px) {
  .k-header__search--city-picker select {
    max-width: 150px;
  }
}

.k-leaflets__wrapper {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
@media (min-width: 1024px) {
  .k-leaflets__wrapper {
    margin-left: -15px;
    margin-right: -15px;
  }
}

.k-leaflets__wrapper--xs-mx {
  margin-left: -15px;
  margin-right: -15px;
}

.k-leaflets__wrapper--scroll-x {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: scroll;
}

.k-leaflets__wrapper.mx-0 {
  margin: 0;
}

.k-leaflets__item {
  box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.2);
  position: relative;
  flex: 0 0 calc(50% - 12px);
  transition: transform 0.2s ease-in-out;
  border-radius: 8px;
}
@media (min-width: 768px) {
  .k-leaflets__item {
    flex: 0 0 calc(33.333% - 12px);
  }
}
@media (min-width: 1180px) {
  .k-leaflets__item {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 0 0 calc(20% - 12px);
  }
}

.k-leaflets__info-wrapper {
  display: flex;
  flex-direction: column;
  height: 70px;
}

.k-leaflets__info {
  display: flex;
  align-items: center;
  padding: 0 8px;
  margin-bottom: 8px;
  flex: 1;
}

.k-leaflets__info img {
  width: 28px;
  height: auto;
  margin-right: 8px;
}

.k-leaflets__info-wrapper .k-leaflets__date {
  padding: 0 8px;
  margin-bottom: 4px;
}

.blur-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 8px 8px 0 0;
  z-index: -1;
}

.blur-image {
  filter: blur(30px);
}

.k-leaflets__item .k-leaflets__link {
  height: 220px;
  overflow: hidden;
}
@media (min-width: 1024px) {
  .k-leaflets__item .k-leaflets__link {
    height: 280px;
  }
}

.k-leaflets__item:hover {
  transform: scale(1.02);
}

.k-leaflets__item:hover .k-leaflets__title a {
  text-decoration: underline;
}

.k-leaflets__item--mobile {
  flex: 0 0 100%;
}
@media (min-width: 768px) {
  .k-leaflets__item--mobile {
    display: none;
  }
}

.k-leaflets__item--first {
  width: 100%;
  flex: 0 0 100%;
  height: 312px;
  overflow: hidden;
  box-shadow: none;
}
@media (min-width: 768px) {
  .k-leaflets__item--first {
    flex: 0 0 33%;
  }
}
@media (min-width: 1180px) {
  .k-leaflets__item--first {
    flex: 0 0 40%;
  }
}

.k-leaflets__large-wrapper {
  position: relative;
  width: 100%;
}
@media (min-width: 1024px) {
  .k-leaflets__large-wrapper {
    width: 40%;
  }
}

.k-leaflets__large {
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}
.k-leaflets__large .k-leaflets__large-thumbnail {
  position: relative;
  border-radius: 10px 10px 0 0;
  justify-content: center;
  display: flex;
  height: 220px;
  overflow: hidden;
}
.k-leaflets__large .k-leaflets__large-thumbnail img {
  max-width: 242px;
  width: 100%;
  height: auto;
  margin-bottom: -4px;
}
.k-leaflets__large .k-leaflets__large-thumbnail .chevron-right {
  cursor: pointer;
  transition: 0.2s ease-in-out;
  position: absolute;
  right: 8px;
  top: 50%;
  width: 42px;
  height: 42px;
  padding: 8px;
  border-radius: 50%;
  transform: translateY(-50%);
  border: 1px solid transparent;
  background-color: hsla(0, 0%, 100%, 0.8);
}
.k-leaflets__large .k-leaflets__large-thumbnail .chevron-right:hover {
  border: 1px solid #bc2026;
}
@media (min-width: 768px) {
  .k-leaflets__large .k-leaflets__large-thumbnail {
    height: 336px;
  }
}
.k-leaflets__large .k-leaflets__large-detail {
  display: flex;
  align-items: center;
  padding: 8px;
  min-height: 66px;
  flex-direction: column;
}
@media (min-width: 1024px) {
  .k-leaflets__large .k-leaflets__large-detail {
    flex-direction: row;
  }
}
.k-leaflets__large .k-leaflets__large-detail .logo {
  display: flex;
  align-items: center;
  gap: 10px;
}
.k-leaflets__large .k-leaflets__large-detail .logo img {
  width: 36px;
  height: auto;
}
.k-leaflets__large .k-leaflets__large-detail .logo a {
  font-weight: 700;
  font-size: 18px;
}
.k-leaflets__large .k-leaflets__large-detail .logo a:hover {
  text-decoration: underline;
}
.k-leaflets__large .k-leaflets__large-detail .note {
  font-size: 10px;
}

.k-leaflets__large-detail-title {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-items: flex-start;
}

.k-leaflets__large-detail .k-leaflets__button {
  margin: 0;
  width: 300px;
  height: 40px;
  font-size: 14px;
  font-weight: 700;
  border-radius: 10px;
}
@media (min-width: 1024px) {
  .k-leaflets__large-detail .k-leaflets__button {
    width: 166px;
  }
}

.k-leaflets__large-detail-title .k-leaflets__date {
  padding: 0;
}

.k-leaflets__button {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  font-size: 14px;
  color: #fff;
  background: #009e74;
  padding: 1rem;
  margin: 8px;
  font-weight: bold;
}

@media (min-width: 1180px) {
  .k-leaflets__button:hover {
    background: rgb(0, 107, 78.5569620253);
  }
}

.k-leaflets__button--secondary {
  border: 1px dashed #009e74;
  background: #fff;
  color: #009e74;
}

@media (min-width: 1180px) {
  .k-leaflets__button--secondary:hover {
    color: #fff;
    background: #009e74;
  }
}

.adslot-1 {
  width: 100%;
  height: 312px;
}
@media (min-width: 768px) {
  .adslot-1 {
    flex: 0 0 33%;
  }
}
@media (min-width: 1180px) {
  .adslot-1 {
    flex: 0 0 40%;
  }
}

.adslot-2 {
  width: 100%;
  height: 312px;
}
@media (min-width: 768px) {
  .adslot-2 {
    height: 280px;
  }
}

.k-leaflets__title {
  position: relative;
  display: block;
  color: #000;
  font-size: 12px;
  font-weight: 700;
  line-height: 1.5;
  padding: 0 8px;
  max-height: 36px;
  overflow: hidden;
}

.k-leaflets__date {
  color: #000;
  font-size: 12px;
  line-height: 1.5;
  padding: 0 8px;
}

.k-leaflets__link {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.k-leaflets__image {
  position: relative;
  display: block;
  max-width: 100%;
  height: auto;
  margin: auto;
  padding: 8px;
  border-radius: 12px;
}

.k-leaflets__wrapper--4 .k-leaflets__item {
  flex: 0 0 calc(50% - 12px);
}
@media (min-width: 768px) {
  .k-leaflets__wrapper--4 .k-leaflets__item {
    flex: 0 0 calc(33% - 12px);
  }
}
@media (min-width: 1024px) {
  .k-leaflets__wrapper--4 .k-leaflets__item {
    flex: 0 0 calc(25% - 12px);
  }
}

.k-leaflets__wrapper--3 .k-leaflets__item {
  flex: 0 0 calc(50% - 12px);
}
@media (min-width: 768px) {
  .k-leaflets__wrapper--3 .k-leaflets__item {
    flex: 0 0 calc(33% - 12px);
  }
}

.k-leaflet__ads-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  justify-content: space-evenly;
}

.k-leaflets__link.expired:before,
.lf__box-image-wrapper.expired:before {
  position: absolute;
  display: block;
  content: "";
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.6);
  z-index: 1;
}

.k-leaflets__corner {
  position: absolute;
  display: flex;
  justify-content: center;
  bottom: 0rem;
  left: 50%;
  margin-left: -65px;
  top: -13px;
  width: 130px;
  bottom: auto;
  border-radius: 10px;
  padding: 0.75rem;
  font-weight: 700;
  background-color: #4CAF50;
  color: #fff;
  font-size: 14px;
  text-align: center;
}

@media only screen and (max-width: 767px) {
  .k-leaflets__large-wrapper {
    width: 100%;
    padding: 0 1rem;
    margin-right: 0;
  }
}
@media (max-width: 1179px) {
  .k-leaflets__item {
    grid-template-rows: auto 1fr;
  }
}
.k-leaflets__bubble {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 9;
}

.k-leaflets__bubble-primary {
  padding: 0.5rem 1rem;
  background: #009e74;
  border-radius: 18px;
  font-size: 12px;
  color: #fff;
  font-weight: 700;
  margin: 0 0.25rem;
}

.k-leaflets__bubble-top {
  padding: 0.5rem 1rem;
  background: #f8992b;
  border-radius: 18px;
  font-size: 12px;
  color: #fff;
  font-weight: 700;
  margin: 0 0.25rem;
}

.k-leaflets__bubble small {
  background: #fff;
  padding: 1rem;
  border-radius: 4px;
  margin: 0 0.25rem;
}

@media only screen and (max-width: 420px) {
  .k-leaflets__info-wrapper {
    height: 90px;
  }
}
.inline-flex {
  display: inline-flex;
}

.overflow-x-auto {
  overflow-x: auto;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.k-lf-layout .container {
  display: flex;
  width: 100%;
  flex-direction: column;
}
@media (min-width: 1024px) {
  .k-lf-layout .container {
    width: 995px;
    flex-direction: row;
  }
}
@media (min-width: 1200px) {
  .k-lf-layout .container {
    width: 1170px;
  }
}
@media (min-width: 1510px) {
  .k-lf-layout .container {
    width: 1170px;
  }
}

.k-lf-layout .container.d-block {
  display: block;
}

.leaflet__content {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
}
@media (min-width: 1024px) {
  .leaflet__content {
    flex-direction: row;
    width: calc(100% - 315px);
  }
}

.leaflet__content.w100 {
  width: 100%;
}

.leaflet__aside {
  position: relative;
  display: block;
}
@media (min-width: 1200px) {
  .leaflet__aside {
    display: block;
    width: 300px;
    order: -1;
    margin: 0;
    margin-right: 15px;
    padding-top: 1rem;
  }
}
.leaflet__sidebar {
  margin-top: 0;
}
@media (min-width: 1024px) {
  .leaflet__sidebar {
    margin-left: 15px;
    flex: 0 0 160px;
    width: 160px;
    padding-top: 1rem;
  }
}
@media (min-width: 1200px) {
  .leaflet__sidebar {
    flex: 0 0 300px;
    width: 300px;
  }
}

.leaflet__detail-header {
  margin-top: 1.5rem;
}
@media (min-width: 768px) {
  .leaflet__detail-header {
    margin-top: 1rem;
  }
}

.page-header__title {
  font-size: 20px;
  font-weight: 700;
}
@media (min-width: 768px) {
  .page-header__title {
    font-size: 24px;
  }
}

.page-header__text {
  margin-bottom: 0;
  font-size: 12px;
}
@media (min-width: 768px) {
  .page-header__text {
    font-size: 14px;
  }
}

.page-header__text a {
  text-decoration: underline;
}

.page-header__text a:hover {
  text-decoration: none;
}

.leaflet__date {
  display: inline-block;
  font-size: 20px;
  font-weight: 400;
}
@media (min-width: 768px) {
  .leaflet__date {
    font-size: 28px;
  }
}

.lf__box {
  position: relative;
  display: block;
  text-align: left;
  margin-bottom: 1rem;
}
@media (min-width: 1024px) {
  .lf__box {
    border: 1px solid #d9d9d9;
    padding: 1rem;
  }
}

@media (min-width: 1024px) {
  .lf__box-lg-border {
    border: 0;
    padding: 0rem;
  }
}
@media (min-width: 1200px) {
  .lf__box-lg-border {
    border: 1px solid #d9d9d9;
    padding: 1rem;
  }
}

.lf__box-title {
  font-size: 14px;
  margin-top: 0;
  margin-bottom: 1rem;
  font-weight: 400;
}
@media (min-width: 1200px) {
  .lf__box-title {
    font-size: 18px;
  }
}

.lf__box-wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
@media (min-width: 1200px) {
  .lf__box-wrapper {
    flex-direction: column;
  }
}

.lf__box-item {
  position: relative;
  display: flex;
  text-align: center;
  flex: 0 0 33%;
  padding: 0.5rem;
}
@media (min-width: 1200px) {
  .lf__box-item {
    text-align: left;
    padding: 0;
  }
}

@media (min-width: 1024px) {
  .lf__box-item--md-100 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.lf__box-item--md-100 .lf__box-image-wrapper {
  flex: 0 0 100px;
}

.lf__box-item:last-child {
  margin-bottom: 0;
}

.lf__box-item:hover .lf__box-link {
  text-decoration: underline;
}

.lf__box-item:hover .lf__box-image-wrapper:before {
  position: absolute;
  display: block;
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.25);
  z-index: 9;
}

.lf__box-image-wrapper {
  position: relative;
  display: block;
  flex: 0 0 60px;
  height: auto;
}

.lf__box-image--medium {
  flex: 0 0 100px;
}

.leaflet-preview {
  position: relative;
  display: block;
  width: 100%;
  max-width: 870px;
  margin-left: auto;
  margin-right: auto;
}

.leaflet-preview img {
  position: relative;
  display: block;
  max-width: 100%;
  height: auto;
}

.leaflet-list-compact__item {
  border-color: #d9d9d9;
}

.leaflet__detail-header {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
  z-index: 1;
}
@media (min-width: 768px) {
  .leaflet__detail-header {
    flex-direction: row;
  }
}

.leaflet__detail-header-content {
  position: relative;
  display: block;
  flex: 1 1 auto;
}

.leaflet__detail-header-side {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 0 0 80px;
  height: 60px;
  order: -1;
  margin-bottom: 20px;
}
@media (min-width: 768px) {
  .leaflet__detail-header-side {
    flex: 0 0 160px;
    order: 0;
    margin-bottom: 0;
  }
}

.leaflet__detail-header-logo-wrapper {
  position: relative;
  display: flex;
  height: auto;
  align-items: center;
  justify-content: center;
}

.leaflet__detail-header-logo {
  position: relative;
  display: block;
  margin: auto;
  width: auto;
  max-width: 80px;
  height: auto;
  max-height: 60px;
}

.leaflet__detail-header--mobile-row {
  flex-direction: row;
  text-align: left;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding: 0 1rem;
}
@media (min-width: 1200px) {
  .leaflet__detail-header--mobile-row {
    padding: 0;
    margin-bottom: 2rem;
    align-items: center;
  }
}

.leaflet__detail-header--mobile-row .page-header__title {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.k-subdomain.leaflet__detail-header--mobile-row .page-header__title {
  font-size: 26px;
  font-weight: normal;
  line-height: 1.25;
}

.leaflet__detail-header--mobile-row .page-header__title,
.leaflet__detail-header--mobile-row .page-header__text,
.leaflet__detail-header--mobile-row .leaflet__date {
  text-align: left;
}

.leaflet__detail-header--mobile-row .leaflet__detail-header-side {
  margin-bottom: 0;
  margin-right: 1.5rem;
}
@media (min-width: 768px) {
  .leaflet__detail-header--mobile-row .leaflet__detail-header-side {
    display: flex;
    justify-content: flex-end;
    margin-right: 0;
  }
}

.leaflet__detail-header--mobile-row .leaflet__detail-header-logo {
  margin: 0;
}

@media (min-width: 1200px) {
  .k-lf-layout--wide-content .leaflet__content {
    width: calc(100% - 175px);
  }
}

@media (min-width: 1024px) {
  .k-lf-layout--wide-content .leaflet__sidebar {
    margin-left: 15px;
    flex: 0 0 160px;
    width: 160px;
    padding-top: 2rem;
  }
}

@media (min-width: 1510px) {
  .k-lf-layout--fixed-container .container {
    width: 1170px;
  }
}

.leaflet__detail-header--bigger-logo .leaflet__detail-header-side {
  justify-content: flex-start;
  flex: 0 0 auto;
  margin-right: 2rem;
  padding: 2rem;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.15);
}

.leaflet__detail-header--bigger-logo.leaflet__detail-header--mobile-row {
  align-items: center;
}

.leaflet-circle-wrapper {
  position: absolute;
  top: 40%;
  width: 100%;
}

.leaflet-circle {
  position: absolute;
  display: inline-flex;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  color: #fff;
  background-color: #009e74;
  opacity: 0.8;
  box-shadow: rgb(96, 96, 96) 1px 1px 3px 0px;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-weight: 600;
  padding: 0px 12px;
  z-index: 9;
  left: -1.5rem;
}

.leaflet-circle:hover {
  opacity: 1;
}

.leaflet-circle__right {
  left: auto;
  right: -1.5rem;
  rotate: 180deg;
}

.k-footer {
  position: relative;
  display: block;
}

.k-footer .container {
  padding-top: 5rem;
  padding-bottom: 3rem;
  border-top: 1px solid #D9D9D9;
}

.k-footer svg {
  width: 200px;
  max-width: 100%;
}

.k-footer__wrapper {
  position: relative;
  display: flex;
  width: 100%;
  flex-direction: row;
  flex-wrap: wrap;
  text-align: left;
}
@media (min-width: 1180px) {
  .k-footer__wrapper {
    flex-wrap: nowrap;
  }
}

.k-footer__column {
  flex: 1 1 50%;
  margin-bottom: 2rem;
}
@media (min-width: 1024px) {
  .k-footer__column {
    flex: 1 1 auto;
    margin-bottom: 0;
  }
}

.k-footer__column--first {
  flex: 1 1 auto;
  margin-bottom: 1rem;
  order: 1;
}
@media (min-width: 768px) {
  .k-footer__column--first {
    flex: 0 0 230px;
    order: 0;
    padding-right: 2rem;
  }
}
@media (min-width: 1024px) {
  .k-footer__column--first {
    flex: 0 0 320px;
  }
}

.k-search__item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #d9d9d9;
}

.k-search__item:hover {
  background-color: #eaeaea;
}

.k-search__image-wrapper {
  position: relative;
  display: flex;
  flex: 0 0 60px;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.k-search__image {
  position: relative;
  display: block;
  width: auto;
  max-width: 100%;
  height: auto;
  max-height: 30px;
  height: auto;
}

.k-search__name {
  position: relative;
  display: block;
  font-size: 14px;
  color: #000;
}

.nice-select {
  border: 0;
  padding-left: 1rem;
}

.k-shop {
  position: relative;
  display: flex;
  flex-wrap: wrap;
}

.k-shop__item {
  position: relative;
  display: block;
  flex: 0 0 33.3333333333%;
  margin-bottom: 3rem;
}
@media (min-width: 1180px) {
  .k-shop__item {
    flex: 0 0 16.6666666667%;
  }
}

.k-shop__item:hover .k-shop__title {
  text-decoration: underline;
}

.k-shop__image-wrapper {
  position: relative;
  display: flex;
  height: 100px;
  align-items: center;
  justify-content: center;
  margin: auto;
  margin-bottom: 1rem;
}

.k-shop__image-wrapper img {
  position: relative;
  display: block;
  width: auto;
  max-width: 80px;
  height: auto;
  max-height: 70px;
  height: auto;
}

.k-shop__title {
  position: relative;
  display: block;
  color: #000;
  font-size: 12px;
  font-weight: 700;
  line-height: 1.5;
  text-align: center;
  margin: 0 1rem;
}

.k-shop__bubble {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 40px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #fff;
  font-weight: 700;
  padding: 0.5rem;
  background-color: #009e74;
}

.k-shop__bubble.green {
  color: green;
  border: 1px solid green;
  background-color: #fff;
}

.k-shop__input {
  position: relative;
  display: block;
  width: auto;
  flex: 1 1 auto;
  height: 32px;
  margin-left: 1px;
  padding-left: 1rem;
  border: 1px solid #d9d9d9;
  outline: none;
  line-height: 16px;
  color: #2c2c2c;
}

.k-shop__submit {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto;
  min-width: 67px;
  height: 32px;
  margin-right: 4px;
  background: #fff;
  border: 1px solid #009e74;
  font-weight: bold;
  color: #009e74;
  font-size: 12px;
  line-height: 14px;
  text-transform: uppercase;
  cursor: pointer;
  -webkit-appearance: none;
}
@media (min-width: 768px) {
  .k-shop__submit {
    min-width: 87px;
  }
}

.k-shop__submit:hover {
  background-color: #009e74;
  color: #fff;
}

.k-tabs__tabLink {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  color: #c4c4c4;
  transition: color 0.3s ease;
  font-size: 18px;
  font-weight: 700;
  width: 100%;
  padding-bottom: 5px;
}
.k-tabs__tabLink svg {
  margin-right: 4px;
  fill: currentColor;
}
.k-tabs__tabLink:hover {
  color: #009e74;
  transition: color 0.2s;
}
.k-tabs__tabLink:not(.active)::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0%;
  height: 2px;
  background: #009e74;
  transition: width 0.3s ease-out, left 0.3s ease-out;
}
.k-tabs__tabLink:not(.active):hover::after {
  width: 100%;
  left: 0;
}
.k-tabs__tabLink.active {
  color: #009e74;
  padding-bottom: 3px;
  border-bottom: 2px solid #009e74;
}

.k-tabs_tab {
  display: none;
}

.k-tabs_tab.active {
  display: block;
}

.k-tabs__buttons {
  display: flex;
  text-align: center;
  gap: 12px;
  margin-bottom: 20px;
}

@media only screen and (max-width: 767px) {
  .k-tabs__tabLink {
    font-size: 14px;
    flex-direction: column;
  }
  .k-tabs__tabLink svg {
    width: 18px;
    height: 18px;
  }
}
.k-paginator__wrapper {
  position: relative;
  display: flex;
  min-height: 50px;
  align-items: center;
  justify-content: center;
  flex-direction: row;
}

.k-paginator__item {
  position: relative;
  display: flex;
  width: 34px;
  height: 32px;
  text-align: center;
  font-weight: 300;
  color: #323232; /* text color */
  font-size: 14px;
  text-decoration: none;
  margin-right: 5px;
  line-height: 30px;
  align-items: center;
  justify-content: center;
  border: 1px solid #ccc;
  background-color: rgb(245, 245, 245);
}
.k-paginator__item:hover {
  border-color: #009e74;
  color: #009e74;
  font-weight: 700;
}
.k-paginator__item.active {
  border-color: #009e74;
  color: #009e74;
  font-weight: 700;
  background-color: #009e74;
  color: #fff;
}
@media (min-width: 768px) {
  .k-paginator__item {
    width: 50px;
    height: 44px;
    font-size: 16px;
  }
}

.k-paginator__separator {
  position: relative;
  display: block;
  width: 30px;
  height: 30px;
  text-align: center;
  font-weight: 300;
  color: #323232; /* text color */
  font-size: 14px;
  text-decoration: none;
  margin-right: 5px;
  line-height: 30px;
}

.k-paginator__button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  border: 1px solid #009e74;
  color: #009e74;
  font-size: 16px;
  font-weight: 700;
  width: auto;
  padding: 0 1rem;
  margin: 1rem;
  height: 32px;
  background-color: rgb(245, 245, 245);
}
@media (min-width: 768px) {
  .k-paginator__button {
    height: 44px;
  }
}
.k-paginator__button:hover {
  text-decoration: underline;
}

.k-paginator__pages {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  flex-grow: 1;
  height: 40px;
  border: 1px solid #CCC;
  background: rgba(217, 217, 217, 0);
  color: #000;
  text-align: center;
  font-weight: 400;
  line-height: normal;
}
@media (min-width: 768px) {
  .k-paginator__pages {
    max-width: 150px;
  }
}

.k-paginator__item--contest {
  position: relative;
  background-color: #009e74;
  cursor: default;
  border: 1px solid transparent;
}

.k-paginator__item--contest:hover {
  color: #000;
  border: 1px solid transparent;
  font-weight: normal;
}

.k-paginator__item--contest span {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  top: -29px;
  font-size: 12px;
  font-weight: 700;
  line-height: 33px;
  text-align: center;
  width: 52px;
  height: 22px;
  border-radius: 50px;
  background-color: #FFC600;
}

.k-paginator__button-desktop-text {
  display: none;
}
@media (min-width: 768px) {
  .k-paginator__button-desktop-text {
    display: block;
  }
}

.k-content {
  padding-bottom: 2rem;
}

.k-content.pb-0 {
  padding-bottom: 0;
}

.k-content h2,
.k-content h3,
.k-content h4 {
  margin-top: 2rem;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.k-content h2 {
  font-size: 20px;
}

.k-content h3 {
  font-size: 18px;
}

.k-content h4 {
  font-size: 14px;
}

.k-content p {
  color: #2c2c2c;
  line-height: 1.7;
  margin-bottom: 1rem;
}

.k-content p a {
  color: #2c2c2c;
  text-decoration: underline;
}

.k-content p a:hover {
  text-decoration: none;
}

.k-content ul,
.k-content ol {
  padding-left: 2rem;
}

.k-content li {
  color: #2c2c2c;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.k-content li a {
  color: #2c2c2c;
  text-decoration: underline;
}

.k-content li a:hover {
  text-decoration: none;
}

.k-content figure {
  margin: 2rem 0;
}

.k-content img {
  position: relative;
  display: block;
  max-width: 100%;
  height: auto;
}

.k-content table {
  border-spacing: 0;
  width: 100%;
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.k-content tr:first-child td {
  background-color: #c6c6c6;
}

.k-content tr:last-child td {
  border-bottom: 0;
}

.k-content td {
  height: 50px;
  border-bottom: 1px solid grey;
  border-right: 1px solid grey;
  padding: 0.75rem 1rem;
  line-height: 1.5;
}

.k-content td:last-child {
  border-right: 0;
}

.k-content td p {
  margin-bottom: 0;
}

@media (min-width: 1180px) {
  .k-content--medium {
    max-width: 785px;
  }
}

.adsbygoogle {
  display: block;
  margin: auto;
  margin-bottom: 1rem;
}

@media (max-width: 767px) {
  .hide-xs {
    display: none !important;
  }
  .srec-xs {
    width: 250px;
    height: 250px;
  }
  .mrec-xs {
    width: 300px;
    height: 250px;
  }
  .lrec-xs {
    width: 336px;
    height: 280px;
  }
  .leaderboard-xs {
    width: 728px;
    height: 90px;
  }
  .mobile-xs {
    width: 320px;
    height: 50px;
  }
  .skyscraper-xs {
    width: 120px;
    height: 600px;
  }
  .halfpage-xs {
    width: 300px;
    height: 600px;
  }
  .banner-xs {
    width: 468px;
    height: 60px;
  }
}
/* small */
@media (min-width: 768px) and (max-width: 991px) {
  .hide-sm {
    display: none;
  }
  .srec-sm {
    width: 250px;
    height: 250px;
  }
  .mrec-sm {
    width: 300px;
    height: 250px;
  }
  .lrec-sm {
    width: 336px;
    height: 280px;
  }
  .leaderboard-sm {
    width: 728px;
    height: 90px;
  }
  .mobile-sm {
    width: 320px;
    height: 50px;
  }
  .skyscraper-sm {
    width: 120px;
    height: 600px;
  }
  .halfpage-sm {
    width: 300px;
    height: 600px;
  }
  .banner-sm {
    width: 468px;
    height: 60px;
  }
}
/* medium */
@media (min-width: 992px) and (max-width: 1199px) {
  .hide-md {
    display: none;
  }
  .srec-md {
    width: 250px;
    height: 250px;
  }
  .mrec-md {
    width: 300px;
    height: 250px;
  }
  .lrec-md {
    width: 336px;
    height: 280px;
  }
  .leaderboard-md {
    width: 728px;
    height: 90px;
  }
  .mobile-md {
    width: 320px;
    height: 50px;
  }
  .skyscraper-md {
    width: 120px;
    height: 600px;
  }
  .halfpage-md {
    width: 300px;
    height: 600px;
  }
  .banner-md {
    width: 468px;
    height: 60px;
  }
}
/* large */
@media (min-width: 1200px) {
  .hide-lg {
    display: none;
  }
  .srec-lg {
    width: 250px;
    height: 250px;
  }
  .mrec-lg {
    width: 300px;
    height: 250px;
  }
  .lrec-lg {
    width: 336px;
    height: 280px;
  }
  .leaderboard-lg {
    width: 728px;
    height: 90px;
  }
  .mobile-lg {
    width: 320px;
    height: 50px;
  }
  .skyscraper-lg {
    width: 120px;
    height: 600px;
  }
  .halfpage-lg {
    width: 300px;
    height: 600px;
  }
  .banner-lg {
    width: 468px;
    height: 60px;
  }
}
.k-profile-header {
  display: flex;
  margin-top: 1rem;
  margin-bottom: 1.5rem;
  padding: 0 1rem;
}
@media (min-width: 768px) {
  .k-profile-header {
    padding: 0;
  }
}

.k-profile-header.my-0 {
  margin-top: 0;
  margin-bottom: 0;
}

.k-profile-header__logo-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100px;
  height: 80px;
  flex: 0 0 100px;
  padding: 1rem;
  border-radius: 4px;
}
@media (min-width: 768px) {
  .k-profile-header__logo-wrapper {
    width: 160px;
    height: 115px;
    flex: 0 0 160px;
    padding: 2rem;
  }
}

.k-profile-header__logo-wrapper picture {
  display: block;
  width: 100%;
}

.k-profile-header__logo {
  position: relative;
  display: block;
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 60px;
  margin: auto;
}
@media (min-width: 768px) {
  .k-profile-header__logo {
    max-height: 75px;
  }
}

.k-profile-header__content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 0 0 calc(100% - 100px);
  padding: 0 1rem;
}
@media (min-width: 768px) {
  .k-profile-header__content {
    flex: 1 1 auto;
    padding: 0 2rem;
  }
}

.k-profile-header__title {
  font-size: 22px;
  font-weight: 600;
  margin-top: 0rem;
  margin-bottom: 0rem;
}
@media (min-width: 768px) {
  .k-profile-header__title {
    font-size: 24px;
  }
}

.k-profile-header__title.mb-0 {
  margin-bottom: 0;
}

.k-profile-header__text {
  line-height: 1.5;
  margin-bottom: 2rem;
}

.k-profile-header__text a {
  text-decoration: underline;
}

.k-profile-header__text a:hover {
  text-decoration: none;
}

.k-profile-header__button-wrapper {
  margin-top: auto;
}

.k-profile-header__button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #bc2026;
  border-radius: 40px;
  font-weight: bold;
  font-size: 12px;
  line-height: 14px;
  padding: 1rem;
  color: #fff;
  background-color: #bc2026;
}
@media (min-width: 768px) {
  .k-profile-header__button {
    min-width: 220px;
  }
}

.k-profile-header__button:hover {
  background-color: rgb(144.4181818182, 24.5818181818, 29.1909090909);
}

@media (min-width: 768px) {
  .k-profile-header--sm-center {
    align-items: center;
  }
}

.k-profile-header--sm-center .k-profile-header__text {
  margin-bottom: 0;
}

@media (min-width: 768px) {
  .k-profile-header__logo-wrapper--smaller {
    width: 100px;
    height: 80px;
    flex: 0 0 100px;
    padding: 1rem;
  }
}

@media (min-width: 768px) {
  .k-profile-header__logo-wrapper--smaller .k-profile-header__logo {
    max-height: 60px;
  }
}

.float-wrapper {
  position: relative;
  display: block;
  text-align: center;
}

.float-wrapper__stop {
  position: relative;
  display: block;
  margin: 0;
  height: 1px;
}

.k-page-extension {
  position: fixed;
  display: flex;
  bottom: 30px;
  right: 10px;
  z-index: 99;
}

.k-page-extension__btn {
  position: relative;
  display: inline-block;
  font-size: 12px;
  color: #fff;
  font-weight: 700;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  background-color: #009e74;
  margin-left: 0.5rem;
}

.k-page-extension__btn:hover {
  background-color: rgb(0, 107, 78.5569620253);
}

.k-page-extension__tag {
  position: relative;
  display: inline-block;
  font-size: 10px;
  color: #000;
  font-weight: 700;
  text-decoration: none;
  padding: 0.5rem;
  border-radius: 4px;
  text-transform: uppercase;
  background-color: #fff;
  border: 1px solid black;
  margin: 0 0.25rem;
}

.k-page-extension__tag--green {
  background-color: #4caf50;
  border-color: #4caf50;
  color: #fff;
}

.k-header {
  box-shadow: none;
  border-bottom: 1px solid #D9D9D9;
}

@media (min-width: 1180px) {
  .k-header__logo-link {
    width: 148px;
    flex: 0 0 148px;
  }
}

@media (min-width: 1024px) {
  .k-leaflets__large-wrapper {
    width: 38%;
  }
}

@media (min-width: 768px) {
  .k-leaflets__large .k-leaflets__large-thumbnail {
    height: 312px;
  }
}