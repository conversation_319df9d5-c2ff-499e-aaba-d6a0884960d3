<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Seo;

use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON><PERSON>\Model\Google\GoogleIndexingClient;
use <PERSON><PERSON><PERSON>\Model\Seo\Entities\GoogleIndex;
use <PERSON><PERSON><PERSON>\Model\Seo\Entities\GoogleIndexProcess;
use <PERSON><PERSON><PERSON>\Model\Seo\Repositories\GoogleIndexRepository;
use Ka<PERSON>ino\Model\Seo\Repositories\GoogleIndexProcessRepository;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;
use Nette\Utils\Json;
use Tracy\Debugger;

class GoogleIndexFacade
{
    /** @var EntityManager */
    private $entityManager;

    /** @var GoogleIndexRepository */
    private $googleIndexRepository;

    /** @var GoogleIndexProcessRepository */
    private $googleIndexProcessRepository;

    /** @var GoogleIndexingClient */
    private $googleIndexingClient;

    public function __construct(
        EntityManager $entityManager,
        GoogleIndexingClient $googleIndexingClient
    ) {
        $this->entityManager = $entityManager;
        $this->googleIndexingClient = $googleIndexingClient;
        $this->googleIndexRepository = $entityManager->getRepository(GoogleIndex::class);
        $this->googleIndexProcessRepository = $entityManager->getRepository(GoogleIndexProcess::class);
    }

    /**
     * Přidá URL do fronty pro indexování
     */
    public function addUrlToIndex(Website $website, string $url): GoogleIndex
    {
        $googleIndex = $this->googleIndexRepository->findByUrl($url);
        
        if (!$googleIndex) {
            $googleIndex = new GoogleIndex($website, $url);
            $this->entityManager->persist($googleIndex);
            $this->entityManager->flush();
        }
        
        return $googleIndex;
    }

    /**
     * Zpracuje URL pro indexování - pošle do Google Indexing API
     */
    public function processIndexingRequest(GoogleIndex $googleIndex): bool
    {
        // Zkontroluj rate limiting
        $dailyCount = $this->googleIndexProcessRepository->countApiRequestsToday();
        $minuteCount = $this->googleIndexProcessRepository->countApiRequestsThisMinute();
        
        if (!$this->googleIndexingClient->canMakeRequest($dailyCount, $minuteCount)) {
            Debugger::log("Rate limit reached. Daily: {$dailyCount}, Minute: {$minuteCount}", 'google-indexing');
            return false;
        }

        // Vytvoř log záznam
        $process = new GoogleIndexProcess($googleIndex, 'indexing', 'URL_UPDATED');
        $process->setRequestData(Json::encode(['url' => $googleIndex->getUrl()]));
        
        // Pošli request do Google API
        $result = $this->googleIndexingClient->requestIndexing($googleIndex->getUrl());
        
        // Zaloguj výsledek
        if ($result['success']) {
            $process->markSuccess($result['status_code'], $result['response']);
            $googleIndex->markIndexingRequested();
            $googleIndex->setLastIndexingStatus('requested');
            
            Debugger::log("Indexing request sent for: " . $googleIndex->getUrl(), 'google-indexing');
        } else {
            $process->markError($result['status_code'], $result['error'] ?? 'Unknown error', $result['response']);
            $googleIndex->setLastErrorMessage($result['error'] ?? 'Unknown error');
            
            Debugger::log("Indexing request failed for: " . $googleIndex->getUrl() . " - " . $result['error'], 'google-indexing');
        }
        
        $this->entityManager->persist($process);
        $this->entityManager->persist($googleIndex);
        $this->entityManager->flush();
        
        return $result['success'];
    }

    /**
     * Zkontroluje stav indexace URL přes Search Console API
     */
    public function checkIndexingStatus(GoogleIndex $googleIndex): bool
    {
        // Zkontroluj rate limiting
        $dailyCount = $this->googleIndexProcessRepository->countApiRequestsToday();
        $minuteCount = $this->googleIndexProcessRepository->countApiRequestsThisMinute();
        
        if (!$this->googleIndexingClient->canMakeRequest($dailyCount, $minuteCount)) {
            Debugger::log("Rate limit reached for status check. Daily: {$dailyCount}, Minute: {$minuteCount}", 'google-indexing');
            return false;
        }

        // Vytvoř log záznam
        $process = new GoogleIndexProcess($googleIndex, 'search_console', 'status_check');
        $process->setRequestData(Json::encode(['url' => $googleIndex->getUrl()]));
        
        // Získej site URL z website
        $siteUrl = $googleIndex->getWebsite()->getDomain();
        
        // Pošli request do Search Console API
        $result = $this->googleIndexingClient->checkIndexingStatus($googleIndex->getUrl(), $siteUrl);
        
        // Zaloguj výsledek
        if ($result['success']) {
            $process->markSuccess($result['status_code'], $result['response']);
            $googleIndex->setLastStatusCheckAt(new \DateTime());
            
            if ($result['is_indexed']) {
                $googleIndex->setIndexed(true);
                $googleIndex->setLastIndexingStatus('indexed');
                Debugger::log("URL is indexed: " . $googleIndex->getUrl(), 'google-indexing');
            } else {
                $googleIndex->setLastIndexingStatus('not_indexed');
                Debugger::log("URL is not indexed yet: " . $googleIndex->getUrl(), 'google-indexing');
            }
        } else {
            $process->markError($result['status_code'], $result['error'] ?? 'Unknown error', $result['response']);
            $googleIndex->setLastErrorMessage($result['error'] ?? 'Unknown error');
            
            Debugger::log("Status check failed for: " . $googleIndex->getUrl() . " - " . $result['error'], 'google-indexing');
        }
        
        $this->entityManager->persist($process);
        $this->entityManager->persist($googleIndex);
        $this->entityManager->flush();
        
        return $result['success'];
    }

    /**
     * Najde URL k indexování
     */
    public function findUrlsToIndex(int $limit = 50): array
    {
        return $this->googleIndexRepository->findUrlsToIndex($limit);
    }

    /**
     * Najde URL ke kontrole stavu
     */
    public function findUrlsToCheckStatus(int $limit = 50): array
    {
        return $this->googleIndexRepository->findUrlsToCheckStatus($limit);
    }

    /**
     * Vrátí statistiky
     */
    public function getStatistics(): array
    {
        return [
            'pending_indexing' => $this->googleIndexRepository->countPendingIndexing(),
            'indexed' => $this->googleIndexRepository->countIndexed(),
            'api_requests_today' => $this->googleIndexProcessRepository->countApiRequestsToday(),
            'api_requests_this_minute' => $this->googleIndexProcessRepository->countApiRequestsThisMinute(),
            'daily_limit' => $this->googleIndexingClient->getDailyLimit(),
            'minute_limit' => $this->googleIndexingClient->getMinuteLimit()
        ];
    }

    /**
     * Najde GoogleIndex podle URL
     */
    public function findByUrl(string $url): ?GoogleIndex
    {
        return $this->googleIndexRepository->findByUrl($url);
    }

    /**
     * Uloží GoogleIndex
     */
    public function save(GoogleIndex $googleIndex): void
    {
        $this->entityManager->persist($googleIndex);
        $this->entityManager->flush();
    }

    /**
     * Najde nedávné logy
     */
    public function getRecentLogs(int $limit = 100): array
    {
        return $this->googleIndexProcessRepository->findRecentLogs($limit);
    }

    /**
     * Najde chybové logy
     */
    public function getErrorLogs(int $limit = 50): array
    {
        return $this->googleIndexProcessRepository->findErrorLogs($limit);
    }
}
