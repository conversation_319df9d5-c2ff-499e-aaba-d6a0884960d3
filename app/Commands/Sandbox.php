<?php

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON><PERSON>\Model\Shops\ContentBlockFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\ContentBlock;

class Sandbox extends Job
{
    private ContentBlockFacade $contentBlockFacade;

    public function __construct(ContentBlockFacade $contentBlockFacade)
    {
        parent::__construct();

        $this->contentBlockFacade = $contentBlockFacade;
    }

    protected function configure(): void
    {
        $this->setName('kaufino:sandbox:run');
    }

    public function start(): void
    {
        $log = $this->onStart();

        $contentBlocksToMerge = [
            'flyers' => [1, 2, 3],
            'about' => [29, 35, 32, 32, 36],
            'contact' => [34, 31],
        ];

        foreach ($contentBlocksToMerge as $type => $contentBlockTypeIds) {
            $contentBlocks = $this->contentBlockFacade->getContentBlocks()
                ->andWhere('cb.contentBlockType IN (:ids)')
                ->setParameter('ids', $contentBlockTypeIds)
                ->andWhere('cb.shop IS NOT NULL')
                ->getQuery()
                ->getResult();

            $blocksByShop = [];
            foreach ($contentBlocks as $contentBlock) {
                $blocksByShop[$contentBlock->getShop()->getId()][$contentBlock->getType()] = $contentBlock;
            }

            foreach ($blocksByShop as $blocks) {
                $content = '';
                $i = 0;
                /** @var ContentBlock $block */
                foreach ($blocks as $block) {
                    if ($i > 0 && $block->getHeading()) {
                        $content .= '<h2>' . $block->getHeading() . '</h2>';
                    }

                    $content .= $block->getContent();
                    $i++;
                }

                $blockToUpdate = $blocks[$type];
                $blockToUpdate->setNewContent($content);
                $this->contentBlockFacade->saveContentBlock($blockToUpdate);
            }
        }

        $this->onFinish($log);
    }
}
