<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Translations;

use <PERSON><PERSON><PERSON>\Model\Configuration;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use Nette\Neon\Neon;
use Nette\Utils\FileSystem;
use Nette\Utils\Finder;
use <PERSON><PERSON><PERSON>\Model\Github\GithubClient;
use Ka<PERSON>ino\Model\Localization\Entities\Localization;
use Nette\Utils\Strings;

class TranslationFacade
{
	private string $localePath;

	private Configuration $configuration;

	private GithubClient $githubClient;

    private LocalizationFacade $localizationFacade;

	public function __construct(string $localePath, Configuration $configuration, GithubClient $githubClient, LocalizationFacade $localizationFacade)
	{
		$this->localePath = $localePath;
		$this->configuration = $configuration;
		$this->githubClient = $githubClient;
        $this->localizationFacade = $localizationFacade;
    }

	public function getDictionaryList(Localization $localization, $withoutExtension = true): array
	{
		$list = [];

		foreach (Finder::findFiles('*.' . $localization->getFullLocale() . '.neon')->in($this->localePath) as $key => $file) {
			$delimiter = Strings::contains($key, '\\') ? '\\' : '/';
			$name = array_values(array_slice(explode($delimiter, $key), -1))[0];

			if ($withoutExtension) {
				$name = Strings::replace($name, '/.' . $localization->getFullLocale() . '.neon/', '');
			}
			$list[] = $name;
		}

		return $list;
	}

	public function getDictionaryData(Localization $localization, string $dictionary, string $branch = GithubClient::DEFAULT_BRANCH)
	{
		$dictionaryFullName = $this->getFullDictionaryFileName($localization, $dictionary);

		$content = $this->githubClient->getFileContent(GithubClient::REPOSITORY_KAUFINO, 'app/locale/' . $dictionaryFullName, $branch);

		return $content ? Neon::decode($content) : null;
	}

	public function getDictionaryDataFromDisk(Localization $localization, string $dictionary)
	{
		try {
			$content = FileSystem::read($this->localePath . $this->getFullDictionaryFileName($localization, $dictionary));
		} catch (\Exception $e) {
			throw new \Exception('Dictionary not found.');
		}

		return $content ? Neon::decode($content) : null;
	}

	public function updateDictionary(Localization $localization, string $dictionary, array $data, $branch = GithubClient::DEFAULT_BRANCH, string $comment = null, bool $needUpload = false): void
	{
		$dictionaryFullName = $this->getFullDictionaryFileName($localization, $dictionary);

		$neonData = Neon::encode($data, Neon::BLOCK);

		if ($needUpload) {
			FileSystem::write($this->localePath . $dictionaryFullName, $neonData);
		}

		$this->githubClient->updateFile(GithubClient::REPOSITORY_KAUFINO, 'app/locale/' . $dictionaryFullName, $neonData, $branch, $comment);
	}

	public function convertDictionaryDataToSingleArray(array $dictionaryData, $parents = '', $array = []): array
	{
		foreach ($dictionaryData as $key => $value) {
			if (is_array($value)) {
				$array = $this->convertDictionaryDataToSingleArray($value, $parents === '' ? $key : ($parents . '.' . $key), $array);
			} else {
				$finalKey = $parents === '' ? $key : ($parents . '.' . $key);
				$array[$finalKey] = $value;
			}
		}

		return $array;
	}

	public function getFullDictionaryName(Localization $localization, string $dictionary): string
	{
		return $dictionary . '.' . $localization->getFullLocale();
	}

	private function getFullDictionaryFileName(Localization $localization, string $dictionary): string
	{
		return $this->getFullDictionaryName($localization, $dictionary) . '.neon';
	}
}
