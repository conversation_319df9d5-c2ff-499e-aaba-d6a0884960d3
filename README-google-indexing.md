# Google Indexing - <PERSON><PERSON><PERSON><PERSON> start

## 🚀 Implementace dokončena

Systém pro automatické indexování letáků do Google je připraven k použití.

## ✅ Co bylo implementováno

### Entity a databáze
- ✅ `GoogleIndex` - hlavní tabulka pro URL indexování
- ✅ `GoogleIndexProcess` - tabulka pro logování API volání
- ✅ Repository třídy pro databázové operace

### API klient
- ✅ `GoogleIndexingClient` - komunikace s Google Indexing API a Search Console API
- ✅ JWT autentifikace pro Service Account
- ✅ Rate limiting (200/den, 600/minutu)

### Facade a logika
- ✅ `GoogleIndexFacade` - hlavní logika pro práci s indexováním
- ✅ Automatické přidávání URL do fronty
- ✅ Posílání requestů do Google API
- ✅ Kontrola stavu indexace
- ✅ Logování všech API volání

### Command
- ✅ `ProcessGoogleIndex` - command pro spuštění procesu
- ✅ Generování URL pro všechny aktivní letáky
- ✅ Zpracování indexovacích requestů
- ✅ Kontrola stavu indexace
- ✅ Zobrazení statistik

### Konfigurace
- ✅ Přidáno do `Configuration` třídy
- ✅ Registrace služeb v `common.neon`
- ✅ Ukázkový `local.example.neon`

## 🔧 Před prvním spuštěním

### 1. Nastavte Google Service Account
```neon
# V local.neon
parameters:
    google:
        serviceAccountKey: '{"type":"service_account",...}'
```

### 2. Vytvořte databázové tabulky
```bash
bin/helper in
php bin/console orm:schema-tool:update --force
```

### 3. Spusťte command
```bash
bin/helper in
php bin/console kaufino:process-google-index
```

## 📊 Funkční logika (podle zadání)

✅ **Vloží URL do indexed_urls, pokud ještě není v DB**
- Automaticky přidává URL všech aktivních letáků

✅ **Pokud is_indexed = 0, pošle URL do Indexing API (a zaloguje)**
- Respektuje rate limiting
- Loguje všechny requesty

✅ **Pravidelně (každých X hodin) kontroluje stav indexace přes GSC API**
- Každých 6 hodin kontroluje stav
- Aktualizuje databázi

✅ **Výsledky aktualizuje v DB**
- Všechny změny se ukládají do databáze

✅ **Po úspěšné indexaci nastaví is_indexed = 1**
- Automatické označení indexovaných URL

✅ **Loguje každé API volání do api_requests_log (úspěšné i chybové)**
- Kompletní log všech API volání v `kaufino_seo_google_index_process`

✅ **Pokud je dosažen denní nebo minutový limit, API volání se neprovede (nebo počká)**
- Automatické rate limiting

## 🎯 Další kroky

1. **Nastavte Google Service Account** s přístupem k Indexing API a Search Console API
2. **Nakonfigurujte credentials** v local.neon
3. **Spusťte databázovou migraci** pro vytvoření tabulek
4. **Otestujte command** manuálně
5. **Nastavte cron** pro automatické spouštění

## 📖 Dokumentace

Detailní dokumentace je v `docs/google-indexing.md`

## 🐛 Troubleshooting

Nejčastější problémy a jejich řešení najdete v dokumentaci.

---

**Status**: ✅ Implementace dokončena a připravena k testování
