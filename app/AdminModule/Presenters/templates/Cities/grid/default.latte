{extends $originalTemplate}

{define col-active}
    {if $item->isActive()}
        <a href="{plink deactivate!, $item->getId()}" class="ajax btn btn-sm"><i class="fa fa-check text-success text-center"></i></a>
    {else}
        <a href="{plink activate!, $item->getId()}" class="ajax"><i class="fa fa-times text-danger text-center"></i></a>
    {/if}
{/define}

{define col-activeOferto}
    {if $item->isActiveOferto()}
        <a href="{plink deactivate!, $item->getId(), 'oferto'}" class="ajax"><i class="fa fa-check text-success text-center"></i></a>
    {else}
        <a href="{plink activate!, $item->getId(), 'oferto'}" class="ajax"><i class="fa fa-times text-danger text-center"></i></a>
    {/if}
{/define}

{define col-activeBrandsKaufino}
    {if $item->isActiveBrandsKaufino()}
        <a href="{plink deactivateBrands!, $item->getId()}" class="ajax btn btn-sm"><i class="fa fa-check text-success text-center"></i></a>
    {else}
        <a href="{plink activateBrands!, $item->getId()}" class="ajax"><i class="fa fa-times text-danger text-center"></i></a>
    {/if}
{/define}

{define col-activeBrandsOferto}
    {if $item->isActiveBrandsOferto()}
        <a href="{plink deactivateBrands!, $item->getId(), 'oferto'}" class="ajax btn btn-sm"><i class="fa fa-check text-success text-center"></i></a>
    {else}
        <a href="{plink activateBrands!, $item->getId(), 'oferto'}" class="ajax"><i class="fa fa-times text-danger text-center"></i></a>
    {/if}
{/define}

{define col-slug}
    {$item->getSlug()}

    {if $item->isActive()}
        <a href="{plink visit!, $item->getId()}" target="_blank" style="font-size: 0.7rem; color: #1677f5; text-decoration: underline">[Kaufino]</a>
    {/if}

    {if $item->isActiveOferto()}
        <a href="{plink visit!, $item->getId(), 'oferto'}" target="_blank" style="font-size: 0.7rem; color: #1677f5; text-decoration: underline">[MrOferto]</a>
    {/if}
{/define}