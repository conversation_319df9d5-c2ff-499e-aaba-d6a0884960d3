<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Seo\GoogleIndexFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\WebsiteFacade;
use Nette\Application\LinkGenerator;
use <PERSON>\Debugger;

class ProcessGoogleIndex extends Job
{
    /** @var LeafletFacade */
    private $leafletFacade;

    /** @var GoogleIndexFacade */
    private $googleIndexFacade;

    /** @var WebsiteFacade */
    private $websiteFacade;

    /** @var LinkGenerator */
    private $linkGenerator;

    public function __construct(
        LeafletFacade $leafletFacade,
        GoogleIndexFacade $googleIndexFacade,
        WebsiteFacade $websiteFacade,
        LinkGenerator $linkGenerator
    ) {
        parent::__construct();
        $this->leafletFacade = $leafletFacade;
        $this->googleIndexFacade = $googleIndexFacade;
        $this->websiteFacade = $websiteFacade;
        $this->linkGenerator = $linkGenerator;
    }

    protected function configure(): void
    {
        $this->setName('kaufino:process-google-index');
    }

    public function start($localizationId = null): void
    {
        $log = $this->onStart();

        $this->processGoogleIndex();

        $this->onFinish($log);
    }

    private function processGoogleIndex(): void
    {
        ini_set('memory_limit', '1024M');
        ini_set('max_execution_time', '1800'); // 30 minut

        echo "Starting Google Index processing...\n";

        // 1. Přidej nové letáky do indexování
        $this->addNewLeafletsToIndex();

        // 2. Zpracuj URL k indexování
        $this->processIndexingRequests();

        // 3. Zkontroluj stav indexace
        $this->checkIndexingStatus();

        // 4. Zobraz statistiky
        $this->showStatistics();

        echo "Google Index processing completed.\n";
    }

    private function addNewLeafletsToIndex(): void
    {
        echo "Adding new leaflets to index...\n";

        $leaflets = $this->leafletFacade->findLeafletsToProcessGoogleIndex(200);
        $addedCount = 0;

        foreach ($leaflets as $leaflet) {
            $urls = $this->generateLeafletUrls($leaflet);

            foreach ($urls as $url) {
                $existingIndex = $this->googleIndexFacade->findByUrl($url['url']);

                if (!$existingIndex) {
                    $this->googleIndexFacade->addUrlToIndex($url['website'], $url['url']);
                    $addedCount++;
                    echo "Added to index: {$url['url']}\n";
                }
            }
        }

        echo "Added {$addedCount} new URLs to index.\n";
    }

    private function processIndexingRequests(): void
    {
        echo "Processing indexing requests...\n";

        $urlsToIndex = $this->googleIndexFacade->findUrlsToIndex(50);
        $processedCount = 0;
        $successCount = 0;

        foreach ($urlsToIndex as $googleIndex) {
            if ($this->googleIndexFacade->processIndexingRequest($googleIndex)) {
                $successCount++;
                echo "✓ Indexing request sent: {$googleIndex->getUrl()}\n";
            } else {
                echo "✗ Failed to send indexing request: {$googleIndex->getUrl()}\n";
            }

            $processedCount++;

            // Pauza mezi requesty pro rate limiting
            sleep(1);
        }

        echo "Processed {$processedCount} indexing requests, {$successCount} successful.\n";
    }

    private function checkIndexingStatus(): void
    {
        echo "Checking indexing status...\n";

        $urlsToCheck = $this->googleIndexFacade->findUrlsToCheckStatus(30);
        $checkedCount = 0;
        $indexedCount = 0;

        foreach ($urlsToCheck as $googleIndex) {
            if ($this->googleIndexFacade->checkIndexingStatus($googleIndex)) {
                if ($googleIndex->isIndexed()) {
                    $indexedCount++;
                    echo "✓ URL is indexed: {$googleIndex->getUrl()}\n";
                } else {
                    echo "⏳ URL not indexed yet: {$googleIndex->getUrl()}\n";
                }
            } else {
                echo "✗ Failed to check status: {$googleIndex->getUrl()}\n";
            }

            $checkedCount++;

            // Pauza mezi requesty pro rate limiting
            sleep(2);
        }

        echo "Checked {$checkedCount} URLs, {$indexedCount} are indexed.\n";
    }

    private function showStatistics(): void
    {
        echo "\n=== Google Index Statistics ===\n";

        $stats = $this->googleIndexFacade->getStatistics();

        echo "Pending indexing: {$stats['pending_indexing']}\n";
        echo "Successfully indexed: {$stats['indexed']}\n";
        echo "API requests today: {$stats['api_requests_today']}/{$stats['daily_limit']}\n";
        echo "API requests this minute: {$stats['api_requests_this_minute']}/{$stats['minute_limit']}\n";
        echo "===============================\n\n";
    }

    private function generateLeafletUrls(Leaflet $leaflet): array
    {
        $urls = [];
        $websites = $this->websiteFacade->findActiveWebsites();

        foreach ($websites as $website) {
            $localization = $website->getLocalization();

            // Zkontroluj, zda je shop aktivní pro daný website
            $shop = $leaflet->getShop();
            $isActive = false;

            switch ($website->getModule()) {
                case 'kaufino':
                    $isActive = $shop->isActiveKaufino();
                    break;
                case 'oferto':
                    $isActive = $shop->isActiveOferto();
                    break;
                case 'letado':
                    $isActive = $shop->isActiveLetado();
                    break;
            }

            if (!$isActive) {
                continue;
            }

            try {
                $linkParams = ['shop' => $shop, 'leaflet' => $leaflet];

                if (!$website->isOfertoCom() && !$website->isOferto()) {
                    $linkParams['region'] = $localization->getRegion();
                }

                $moduleName = $this->transformModuleNameToCamelCase($website->getModule());
                $url = $this->linkGenerator->link($moduleName . ':Leaflet:leaflet', $linkParams);

                $urls[] = [
                    'website' => $website,
                    'url' => $url
                ];

            } catch (\Exception $e) {
                Debugger::log("Failed to generate URL for leaflet {$leaflet->getId()} on website {$website->getId()}: " . $e->getMessage(), 'google-indexing');
            }
        }

        return $urls;
    }

    private function transformModuleNameToCamelCase(string $moduleName): string
    {
        return ucfirst($moduleName);
    }
}
