// Kaufino JavaScript
var oferto = {};

oferto.loader = {
  ready: function (fn) {
    if (document.readyState == "complete") {
      return fn();
    }

    if (window.addEventListener) {
      window.addEventListener("load", fn, false);
    } else if (window.attachEvent) {
      window.attachEvent("onload", fn);
    } else {
      window.onload = fn;
    }
  },
};

oferto.loader.ready(function () {
  // Mobile menu click
  var el_button = document.getElementsByClassName("js-show-all-page");
  var el_preview = document.getElementsByClassName("o-leaflet-preview");

  var el_button = document.getElementsByClassName("js-show-all-page");

  if (el_button && el_button.length > 0) {
    el_button[0].addEventListener("click", function (e) {
      e.preventDefault();
      el_preview[0].classList.add("show");
      el_button[0].classList.add("hide");
    });
  }

  var cookieBanner = document.getElementById("cookiescript_injected");

  if (cookieBanner) {
    cookieBanner.classList.add("slide-up");
  }

  // vybereme tlačítko pomocí classy
  const submitButton = document.querySelector(".js-o-email-line__submit");

  if (submitButton) {
    // přidáme posluchač události kliknutí na tlačítko
    submitButton.addEventListener("click", function (event) {
      // zakážeme výchozí chování (odeslání formuláře)
      event.preventDefault();

      // získáme hodnoty z inputu
      const emailValue = document.querySelector("#o-email-line-email").value;
      const shopValue = document.querySelector("#o-email-line-shop").value;

      // vytvoříme novou instanci XMLHttpRequest objektu
      const xhr = new XMLHttpRequest();

      // nastavíme typ požadavku a adresu endpointu
      xhr.open("POST", "../api/v1/add-email");

      // nastavíme hlavičky požadavku (pokud jsou potřeba)
      xhr.setRequestHeader("Content-Type", "application/json");

      // definujeme, co se stane, když se požadavek dokončí
      xhr.onload = function () {
        if (xhr.status === 200) {
          // pokud je status 200, znamená to úspěšný požadavek
          console.log(xhr.responseText);
        } else {
          // pokud není status 200, došlo k chybě
          console.error("Chyba při odesílání požadavku");
        }
      };

      // vytvoříme objekt s daty, které budeme odesílat
      const data = {
        email: emailValue,
        shopId: shopValue,
      };

      console.log(data);

      // převedeme data do formátu JSON
      const jsonData = JSON.stringify(data);

      // odešleme požadavek s daty
      xhr.send(jsonData);

      // skryjeme formular
      document.querySelector(".o-email-line").style.display = "none";
    });
  }

  // Coupon popup
  var c_el_wrapper = document.getElementsByClassName("k-modal");
  var c_el_code_copy = document.getElementsByClassName("k-modal__code-copy");
  var c_el_close_button = document.getElementsByClassName("k-modal__close");
  var c_el_copy_button = document.getElementsByClassName("k-modal__code-btn");

  // Click coupon link
  var c_js_click_link = document.getElementsByClassName("js-click-link");

  if (c_js_click_link.length > 0) {
    c_js_click_link[0].addEventListener("click", function () {
      var redirect_link = c_js_click_link[0].dataset.popupLink;
      setTimeout(function () {
        window.location.replace(redirect_link);
      }, 250);
    });
  }

  // Click outside Coupon Popup
  document.addEventListener("click", function (event) {
    if (document.getElementsByClassName("k-modal")[0] == event.target) {
      c_el_wrapper[0].classList.remove("show");
    }
  });

  // Click close button
  if (c_el_close_button.length > 0) {
    c_el_close_button[0].addEventListener("click", function () {
      c_el_wrapper[0].classList.remove("show");
    });
  }

  // Copy code
  if (c_el_copy_button.length > 0) {
    c_el_copy_button[0].addEventListener("click", function () {
      /* Get the text field */
      var copyText = document.getElementById("copyInput");

      /* Select the text field */
      copyText.select();
      copyText.setSelectionRange(0, 99999); /* For mobile devices */

      /* Copy the text inside the text field */
      document.execCommand("copy");

      /* Alert the copied text */
      console.log("Copied: " + copyText.value);
      c_el_code_copy[0].classList.add("copied");

      setTimeout(function () {
        c_el_code_copy[0].classList.remove("copied");
      }, 3000);
    });
  }

  const zoomElement = document.querySelector(".zoom");
  const ZOOM_SPEED = 250;

  if (zoomElement) {
    document.addEventListener("scroll", function (e) {
      let scroll = window.scrollY;
      let temp = scroll / ZOOM_SPEED;
      let zoom = 1;

      if (temp < 1) {
        temp = 1;
      } else if (temp > 1.4) {
        temp = 1.4;
      }

      zoom = temp;

      if (e.deltaY > 0) {
        zoomElement.style.transform = `scale(${zoom})`;
      } else {
        zoomElement.style.transform = `scale(${zoom})`;
      }
    });
  }
});
