@charset "UTF-8";
* {
  box-sizing: border-box;
}

*:before,
*:after {
  box-sizing: border-box;
}

html {
  font-size: 10px;
}

body {
  font-family: Nunito, Helvetica Neue, Helvetica, Arial, sans-serif;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  margin: 0;
  background-color: #fff;
  padding: 0px !important;
}

p {
  margin: 0;
  line-height: 1.7;
}

a {
  color: #000;
  text-decoration: none;
}

.link {
  color: #2c2c2c;
  background-color: transparent;
  border: 0;
  cursor: pointer;
}

.link:hover {
  text-decoration: underline;
}

button.link {
  font-size: 14px;
  line-height: 1.25;
}

.container {
  position: relative;
  display: block;
  width: calc(100% - 30px);
  margin: auto;
  padding: 0 1rem;
}
@media (min-width: 1180px) {
  .container {
    width: 1170px;
    padding: 0 3rem;
  }
}

.container--flex {
  display: flex;
  align-items: center;
  padding: 0 1rem;
}

.p-relative {
  position: relative;
}

.d-none {
  display: none;
}

.d-block {
  display: block;
}

@media (min-width: 768px) {
  .d-sm-block {
    display: block;
  }
}

.d-inline-block {
  display: inline-block;
}

.d-flex {
  display: flex;
}

@media (min-width: 768px) {
  .d-sm-flex {
    display: flex;
  }
}

@media (min-width: 1024px) {
  .d-md-flex {
    display: flex;
  }
}

.align-items-center {
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.flex-grow-1 {
  flex-grow: 1;
}

.justify-content-center {
  justify-content: center;
}

@media (min-width: 768px) {
  .justify-content-sm-start {
    justify-content: flex-start;
  }
}

.flex-wrap-no-wrap {
  flex-wrap: nowrap;
}

.flex-wrap {
  flex-wrap: wrap;
}

@media (min-width: 768px) {
  .flex-wrap-sm-no-wrap {
    flex-wrap: nowrap;
  }
}

.flex-direction-row {
  flex-direction: row;
}

.flex-direction-column {
  flex-direction: column;
}

@media (min-width: 768px) {
  .flex-direction-sm-row {
    flex-direction: row;
  }
}

@media (min-width: 1024px) {
  .flex-direction-md-column {
    flex-direction: column;
  }
}

@media (min-width: 1200px) {
  .flex-direction-lg-row {
    flex-direction: row;
  }
}

@media (min-width: 1200px) {
  .flex-direction-lg-column {
    flex-direction: column;
  }
}

.w100 {
  width: 100%;
}

.h100 {
  height: 100%;
}

@media (min-width: 768px) {
  .w-sm-auto {
    width: auto;
  }
}

.pb-0 {
  padding-bottom: 0;
}

.pb-1 {
  padding-bottom: 0.25rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-3 {
  padding-bottom: 1rem;
}

.pb-4 {
  padding-bottom: 1.5rem;
}

.pb-5 {
  padding-bottom: 3rem;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.px-3 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-3 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.px-4 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.py-4 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.px-5 {
  padding-left: 3rem;
  padding-right: 3rem;
}

.py-5 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

@media (min-width: 1200px) {
  .px-lg-0 {
    padding-left: 0rem;
    padding-right: 0rem;
  }
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-3 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.m-auto {
  margin: auto;
}

.mt-0 {
  margin-top: 0;
}

.mt-3 {
  margin-top: 1rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.mt-5 {
  margin-top: 3rem;
}

@media (min-width: 768px) {
  .mt-sm-0 {
    margin-top: 0rem;
  }
}

@media (min-width: 768px) {
  .mt-sm-5 {
    margin-top: 3rem;
  }
}

@media (min-width: 1024px) {
  .mt-md-0 {
    margin-top: 0rem;
  }
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mb-5 {
  margin-bottom: 3rem;
}

.mb-6 {
  margin-bottom: 4.5rem;
}

.mb-7 {
  margin-bottom: 6rem;
}

@media (min-width: 768px) {
  .mb-sm-0 {
    margin-bottom: 0;
  }
}

@media (min-width: 768px) {
  .mb-sm-3 {
    margin-bottom: 1rem;
  }
}

.ml-auto {
  margin-left: auto;
}

@media (min-width: 768px) {
  .ml-sm-auto {
    margin-left: auto;
  }
}

@media (min-width: 1024px) {
  .ml-md-auto {
    margin-left: auto;
  }
}

.ml-0 {
  margin-left: 0;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 1rem;
}

.ml-4 {
  margin-left: 1.5rem;
}

.ml-5 {
  margin-left: 3rem;
}

.mr-auto {
  margin-right: auto;
}

.mr-0 {
  margin-right: 0;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 1rem;
}

.mr-4 {
  margin-right: 1.5rem;
}

.mr-5 {
  margin-right: 3rem;
}

@media (min-width: 768px) {
  .mr-sm-2 {
    margin-right: 0.5rem;
  }
}

@media (min-width: 768px) {
  .ml-sm-3 {
    margin-left: 1rem;
  }
}

@media (min-width: 1200px) {
  .mb-lg-0 {
    margin-right: 0rem;
  }
}

@media (min-width: 1200px) {
  .mb-lg-3 {
    margin-right: 1rem;
  }
}

@media (min-width: 1200px) {
  .mr-lg-3 {
    margin-right: 1rem;
  }
}

@media (min-width: 768px) {
  .pl-sm-3 {
    padding-left: 1rem;
  }
}

@media (min-width: 768px) {
  .pl-sm-4 {
    padding-left: 1.5rem;
  }
}

@media (min-width: 768px) {
  .pr-sm-3 {
    padding-right: 1rem;
  }
}

.overflow-hidden {
  overflow: hidden;
}

.img-responsive {
  display: block;
  max-width: 100%;
  height: auto;
  margin: auto;
}

.ellipsis {
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.fw-regular {
  font-weight: 400;
}

.fw-semibold {
  font-weight: 600;
}

.fw-bold,
.strong {
  font-weight: 700;
}

.tt-uppercase {
  text-transform: uppercase;
}

.td-underline {
  text-decoration: underline;
}

.td-none {
  text-decoration: none;
}

.td-hover-none:hover {
  text-decoration: none;
}

.td-hover-underline:hover {
  text-decoration: underline;
}

.ta-center {
  text-align: center;
}

@media (min-width: 768px) {
  .sm-ta-left {
    text-align: left;
  }
}

.mw-700 {
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.mw-700.ml-0 {
  margin-left: 0;
}

.min-w-300 {
  min-width: 300px;
}

.mw-400 {
  max-width: 400px;
}

.mw-805 {
  max-width: 805px;
}

small {
  font-size: inherit;
}

.fz-xxs {
  font-size: 11px;
}

.fz-s {
  font-size: 12px;
}

.fz-m {
  font-size: 14px;
}

.fz-l {
  font-size: 16px;
}

.fz-xl {
  font-size: 18px;
}

.fz-xxl {
  font-size: 20px;
}

@media (min-width: 768px) {
  .fz-sm-xs {
    font-size: 12px;
  }
}

.color-grey {
  color: #2c2c2c;
}

.color-grey a {
  color: #2c2c2c;
}

.color-light-grey {
  color: #929292;
}

.color-black {
  color: #000;
}

.lh-15 {
  line-height: 1.5;
}

.fw-700 {
  font-weight: 700;
}

.row {
  position: relative;
  display: flex;
  flex-wrap: wrap;
}

.col-6 {
  width: 50%;
  flex: 0 0 50%;
}

.col-12 {
  width: 100%;
  flex: 0 0 100%;
}

@media (min-width: 768px) {
  .col-sm-3 {
    width: 25%;
    flex: 0 0 25%;
  }
}

@media (min-width: 768px) {
  .col-sm-6 {
    width: 50%;
    flex: 0 0 50%;
  }
}

@media (min-width: 768px) {
  .col-sm-9 {
    width: 75%;
    flex: 0 0 75%;
  }
}

@media (min-width: 1180px) {
  .col-md-6 {
    width: 50%;
    flex: 0 0 50%;
  }
}

@media (min-width: 768px) {
  .sm-column-count-2 {
    -moz-column-count: 2;
         column-count: 2;
  }
}

.k-offers__item.hidden,
.k-shop__item.hidden,
.k-tag__inner.hidden,
.k-leaflets__item.hidden {
  display: none;
}

.k-show-more-button {
  font-size: 14px;
  padding: 1rem;
  border: 1px solid #b3b3b3;
  border-radius: 4px;
  margin-right: auto;
  margin-left: auto;
}

.k-show-more-button.hidden {
  display: none;
}

.align-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.space-between {
  justify-content: space-between;
}

.underline {
  text-decoration: underline;
}

.text-center {
  text-align: center;
}

.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-grayscale);
}

.contest-form form {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.contest-form input {
  border: 1px solid #B3B3B3;
  padding: 1rem;
  border-radius: 10px;
  margin: 0.5rem 0;
  margin-right: 0.5rem;
  font-size: 16px;
  min-width: 270px;
}

.contest-form input[type=submit] {
  background-color: #2bb673;
  color: #fff;
  font-weight: bold;
  min-width: 100px;
  cursor: pointer;
}

.contest-box {
  max-width: 720px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1490196078);
  margin: auto;
  padding: 1rem 2rem;
  margin-bottom: 3rem;
}

.contest-box h2 {
  font-size: 20px;
}

.contest-promo {
  position: fixed;
  display: flex;
  align-items: center;
  width: 100%;
  bottom: 0px;
  background-color: #2bb673;
  padding: 1rem 2rem;
}
@media (min-width: 768px) {
  .contest-promo {
    width: auto;
    bottom: 30px;
    right: 30px;
    border-radius: 20px;
    padding: 1.5rem 2rem;
    box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.4);
  }
}

.contest-promo__medal {
  width: 33px;
  height: 44px;
  margin-right: 1rem;
}

.contest-promo span {
  font-size: 18px;
  line-height: 22px;
  text-align: left;
  color: #fff;
  margin-right: 1rem;
}

.contest-promo strong {
  display: block;
  font-weight: 700;
}

.contest-promo__cross {
  margin-left: auto;
  cursor: pointer;
}

.hidden {
  display: none;
}

.kf-click-out {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.15s;
  color: #fff;
}

.kf-click-out:hover {
  opacity: 1;
}

.kf-click-out:hover svg {
  cursor: pointer;
  transform: scale(1.25);
}

.kf-click-out a {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kf-click-out svg {
  background: #2bb673;
  border-radius: 50%;
  box-sizing: border-box;
  fill: #fff;
  width: 1.75em;
  height: 1.75em;
  padding: 0.25em;
  pointer-events: none;
  transition: transform 0.3s cubic-bezier(0.5, -0.15, 0.7, 4);
}

.montagu-slab {
  font-family: "Montagu Slab", serif;
  font-optical-sizing: auto;
  font-weight: 700;
  font-style: normal;
  color: #2bb673;
}

.k-coupon {
  position: relative;
  display: grid;
  min-height: 100px;
  grid-template-rows: auto;
  grid-row-gap: 0;
  grid-column-gap: 1rem;
  text-align: center;
  border-radius: 4px;
  padding: 1rem;
  margin: 1rem;
  border: 1px solid #D9D9D9;
  grid-template-columns: 100px auto;
}
@media (min-width: 768px) {
  .k-coupon {
    grid-column-gap: 2rem;
    grid-template-columns: 110px auto 190px;
  }
}

.k-coupon__box {
  display: flex;
  flex-direction: column;
  align-self: center;
  justify-content: flex-start;
  overflow: hidden;
  color: #2bb673;
}

.k-coupon__box-value {
  display: flex;
  min-height: 60px;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  margin-bottom: 1rem;
}
@media (min-width: 768px) {
  .k-coupon__box-value {
    font-size: 24px;
  }
}

.k-coupon__box-value--small {
  font-size: 18px;
}
@media (min-width: 768px) {
  .k-coupon__box-value--small {
    font-size: 22px;
  }
}

.k-coupon__box-type {
  display: block;
  width: 100%;
  align-self: flex-end;
  color: #fff;
  font-size: 1rem;
  line-height: 100%;
  font-weight: 600;
  text-align: center;
  text-transform: uppercase;
  padding: 5px 2px;
  background: #2bb673;
}

.k-coupon__box-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 35px;
}

.k-coupon__content {
  display: flex;
  flex-flow: column wrap;
  text-align: left;
}

.k-coupon__button {
  position: relative;
  display: flex;
  align-self: center;
  align-items: center;
  justify-content: center;
  height: 50px;
  background: #2bb673;
  border: 1px solid #2bb673;
  border-radius: 40px;
  font-weight: bold;
  color: #fff;
  font-size: 14px;
  cursor: pointer;
  margin-top: 2rem;
  grid-row: 3;
  grid-column: 1/3;
}
@media (min-width: 768px) {
  .k-coupon__button {
    grid-row: auto;
    grid-column: 3/4;
    margin-top: 0;
  }
}

.k-coupon__button:hover {
  background: rgb(33.2533333333, 140.7466666667, 88.9333333333);
}

.k-coupon__box--sale .k-coupon__box-type {
  background: #ff9300;
}

.k-coupon__box--sale {
  color: #ff9300;
}

.k-coupon__button--sale .k-coupon__button-label {
  width: 100%;
}

.k-coupon__button--code:hover:before {
  background-color: rgb(38.1266666667, 161.3733333333, 101.9666666667);
  width: 80%;
}

.k-coupon__button--code:hover:after {
  left: 80%;
}

.k-coupon__button--code:before {
  content: "";
  position: absolute;
  width: 85%;
  height: 100%;
  left: 0;
  top: 0;
  border-top-left-radius: 30px;
  border-bottom-left-radius: 30px;
  transition: width 0.2s ease-out;
  z-index: 5;
  background: #2bb673;
}

.k-coupon__button--code:after {
  left: 85%;
  top: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 48px 0 0 35px;
  transform: rotate(17deg);
  transition: left 0.2s ease-out;
  transform-origin: top left;
  content: "";
  position: absolute;
  z-index: 99;
  display: block;
  border-color: transparent transparent transparent rgb(33.2533333333, 140.7466666667, 88.9333333333);
}

.k-coupon__button-label {
  position: absolute;
  width: 85%;
  z-index: 10;
  top: 50%;
  left: 0%;
  transform: translate(0%, -50%);
}

.k-coupon__button-code {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  top: 0;
  left: 0;
  text-align: right;
  padding: 0 1rem;
  width: 100%;
  height: 100%;
  z-index: 0;
  color: #000;
  border-radius: 30px;
  font-size: 14px;
  font-weight: 600;
  background-color: #fff;
  border: 1px solid #cecece;
  overflow: hidden;
}

.k-coupon-expire {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border-bottom: 1px solid #D9D9D9;
  padding: 1rem;
}

.k-coupon-expire svg {
  display: block;
  width: 30px;
  height: 30px;
  margin-right: 1rem;
}

.k-coupon-expire small {
  margin-right: 1rem;
}

.k-coupon-expire-wrapper {
  max-height: 200px;
  overflow: hidden;
  transition: max-height 0.2s ease-out;
}

.k-coupon-expire__show {
  position: absolute;
  display: flex;
  width: 100%;
  height: 76px;
  left: 0;
  bottom: 0;
  align-items: flex-end;
  justify-content: flex-start;
  background: linear-gradient(rgba(255, 255, 255, 0.1) 0%, #fff 60%);
  cursor: pointer;
}
@media (min-width: 768px) {
  .k-coupon-expire__show {
    font-size: 16px;
  }
}

.k-coupon-expire__show-inner {
  display: flex;
  align-items: center;
}

@media (min-width: 1180px) {
  .k-coupon-expire__show-inner:hover {
    text-decoration: underline;
  }
}

.k-coupon-expire__show-icon {
  margin-left: 8px;
}

.k-coupon-expire__content-btn {
  display: none;
}

.k-coupon-expire__content-btn:checked ~ .k-coupon-expire-wrapper {
  max-height: 600px;
}

.k-coupon-expire__content-btn:checked ~ .k-coupon-expire-wrapper .k-coupon-expire__show {
  display: none;
}

.k-offers {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
}
@media (min-width: 1024px) {
  .k-offers {
    margin-left: -1rem;
    margin-right: -1rem;
  }
}

.k-offers__item {
  position: relative;
  display: flex;
  flex: 0 0 50%;
  padding: 0.5rem;
}
@media (min-width: 768px) {
  .k-offers__item {
    flex: 0 0 25%;
    padding: 1rem;
  }
}
@media (min-width: 1024px) {
  .k-offers__item {
    flex: 0 0 20%;
  }
}

.k-offers__inner {
  position: relative;
  display: flex;
  width: 100%;
  background-color: #f8f9fa;
  padding: 1rem;
  flex-direction: column;
  border-radius: 8px;
}

.k-offers__image-wrapper {
  display: block;
}

.k-offers__image-wrapper picture {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  flex: 0 0 150px;
  height: 150px;
  overflow: hidden;
  margin-bottom: 1rem;
  margin-left: auto;
  margin-right: auto;
}

.k-offers__image {
  display: block;
  width: auto;
  max-width: 100%;
  height: auto;
  max-height: 100%;
  border-radius: 8px;
}

.k-offers__content {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  margin-top: auto;
  text-align: center;
}

.k-offers__title {
  font-size: 16px;
  margin: 0;
  padding: 0;
  line-height: 1.25;
  margin-bottom: 1.5rem;
}

.k-offers__title a {
  color: #000;
  font-weight: 500;
}

.k-offers__title a:hover {
  text-decoration: underline;
}

.k-offers__small {
  display: block;
  color: #2c2c2c;
  font-size: 14px;
  margin-bottom: 0.5rem;
}

.k-offers__small a {
  color: #2c2c2c;
}

.k-offers__small a:hover {
  text-decoration: underline;
}

.k-offers__text {
  font-size: 12px;
  margin-bottom: 0.5rem;
}

.k-offers__right {
  position: relative;
  flex: 0 0 auto;
}

.k-offers__price {
  display: flex;
  align-items: flex-end;
  margin-top: auto;
  justify-content: center;
}

.k-offers__price strong {
  display: block;
  font-size: 22px;
  color: #2bb673;
}

.k-offers__price small {
  font-size: 14px;
  text-decoration: line-through;
  margin-left: auto;
}

.k-offers__link {
  color: #2c2c2c;
  font-size: 12px;
  text-decoration: underline;
  margin-right: 1rem;
}

.k-offers__link:hover {
  text-decoration: none;
}

.k-offers__item.expired .k-offers__inner:before {
  position: absolute;
  display: block;
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(219, 219, 219, 0.9);
}

.k-offers__item.expired .k-offers__inner:after {
  position: absolute;
  display: flex;
  content: attr(data-line);
  color: #2bb673;
  font-size: 18px;
  font-weight: 700;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 9;
  align-items: center;
  justify-content: center;
}

.k-offers--4 .k-offers__item {
  flex: 0 0 50%;
  padding: 0.5rem;
}
@media (min-width: 768px) {
  .k-offers--4 .k-offers__item {
    flex: 0 0 33.3333333333%;
    padding: 1rem;
  }
}
@media (min-width: 1024px) {
  .k-offers--4 .k-offers__item {
    flex: 0 0 25%;
  }
}

.k-offers__badge {
  position: relative;
  top: -5px;
  font-size: 14px;
  line-height: 1.5rem;
  background: #c30404;
  color: #fff;
  border-radius: 0.5rem;
  text-align: center;
  padding: 0.5rem 0.5rem;
  margin-left: 0.5rem;
  font-weight: 600;
}

.k-offers__small--logo {
  position: relative;
  display: flex;
  justify-content: center;
  margin-top: -50px;
}

.k-offers__logo-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  width: 50px;
  height: 50px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  transition: box-shadow 0.2s ease;
  border: 2px solid #bc2026;
  overflow: hidden;
  background-color: #fff;
}

.k-offers__logo-wrapper img {
  max-width: 80%;
  height: auto;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.k-offers__tag {
  color: grey;
  text-transform: uppercase;
  font-weight: 600;
  font-size: 12px;
  text-align: center;
}

.k-offers__tag.red {
  color: #2bb673;
}

.k-offers__tag.orange {
  color: #ff9800;
}

.k-offers__tag.green {
  color: #4caf50;
}

.k-modal {
  position: fixed;
  display: none;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.k-modal.show {
  display: flex;
}

.k-modal__container {
  position: relative;
  background-color: #fff;
  width: calc(100% - 32px);
  max-width: 600px;
  max-height: 95vh;
  overflow-y: auto;
}

.k-modal__close {
  position: absolute;
  padding: 0;
  border: 0;
  top: 1.5rem;
  right: 1.5rem;
  outline: none;
  color: #000;
  font-size: 20px;
  line-height: 1rem;
  cursor: pointer;
  background: transparent;
  z-index: 9;
}

.k-modal__close:before {
  content: "✕";
}

.k-modal__content {
  padding: 3rem;
}

.k-modal__code-copy {
  display: flex;
  justify-content: center;
  padding-bottom: 2rem;
}

.k-modal__code-copy.copied .k-modal__code-input {
  position: relative;
  border-color: green;
  color: green;
  outline: none;
}

.k-modal__code-copy.copied .k-modal__copied {
  position: absolute;
  display: block;
  top: 5px;
  left: 1px;
  border-color: green;
  color: green;
  background-color: #fff;
  padding: 10px;
  font-weight: 700;
  text-transform: uppercase;
}

.k-modal__copied {
  display: none;
}

.k-modal__code-input,
.k-modal__code-btn {
  display: flex;
  height: 45px;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: 700;
  padding: 1rem;
  border: 1px solid #000;
}

.k-modal__code-input {
  width: 180px;
}

.k-modal__code-btn {
  min-width: 100px;
  background: #3b3030;
  color: #fff;
  cursor: pointer;
}

.k-modal__code-btn:hover {
  background: rgb(87.1214953271, 70.8785046729, 70.8785046729);
}

.k-modal__btn {
  height: 45px;
  max-width: 300px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: 700;
  background: #2bb673;
  padding: 8px;
  border: 0;
  border-radius: 30px;
}

.k-modal__btn:hover {
  background: rgb(33.2533333333, 140.7466666667, 88.9333333333);
}

.k-modal .k-coupon {
  margin-bottom: 0;
  border: 0;
}
@media (min-width: 768px) {
  .k-modal .k-coupon {
    padding: 2rem;
    grid-template-columns: 120px auto;
  }
}

.k-modal .k-coupon__content {
  grid-column: 2/3;
}

@media (min-width: 768px) {
  .k-content p,
  .k-content li,
  .k__text,
  .k__text p {
    font-size: 16px;
  }
}

.ocom-coupon__wrapper {
  display: flex;
  flex-wrap: wrap;
}

.ocom-coupon__wrapper .k-coupon {
  flex: 1 1 auto;
  grid-template-columns: 110px auto;
}
@media (min-width: 1024px) {
  .ocom-coupon__wrapper .k-coupon {
    flex: 1 1 calc(50% - 2rem);
  }
}

.ocom-coupon__wrapper .k-coupon__button {
  margin-top: 2rem;
  grid-row: 3;
  grid-column: 1/3;
}

.k-profile-header__button {
  background-color: #2bb673;
  border-color: #2bb673;
}

.k-profile-header__button:hover {
  background-color: rgb(33.2533333333, 140.7466666667, 88.9333333333);
}

.k-offers__inner {
  border: 0;
  padding: 0;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e5e5;
}

.k-profile-header__logo-wrapper--smaller {
  padding: 1rem;
  margin-bottom: 1rem;
}
@media (min-width: 1024px) {
  .k-profile-header__logo-wrapper--smaller {
    margin-bottom: 0;
  }
}

.ocom-shop .k-profile-header__title {
  font-size: 24px;
  font-weight: 700;
}

@media (min-width: 768px) {
  .ocom-shop .k-coupon {
    grid-template-columns: 160px auto 220px;
  }
}
@media (min-width: 1024px) {
  .ocom-shop .k-coupon {
    min-height: 120px;
    padding: 1rem 2rem;
  }
}

@media (min-width: 1024px) {
  .ocom-shop .k-coupon__content {
    justify-content: center;
  }
}

@media (min-width: 1024px) {
  .ocom-shop .k-coupon__box-value {
    font-size: 32px;
  }
}

@media (min-width: 1024px) {
  .ocom-shop .k-coupon__box-type {
    font-size: 12px;
    line-height: 1.25;
    padding: 0.5rem;
  }
}

@media (min-width: 768px) {
  .ocom-shop .k-coupon__button {
    font-size: 16px;
    height: 60px;
  }
}

@media (min-width: 768px) {
  .ocom-shop .k-coupon__button--code:after {
    transform: rotate(33deg);
  }
}

@media (min-width: 768px) {
  .ocom-shop .k-modal .k-coupon {
    grid-template-columns: 160px auto;
  }
}

@media (min-width: 1024px) {
  .ocom-offer__title {
    font-size: 20px;
    line-height: 1.5;
  }
}

.ocom-shop .k-profile-header {
  flex-direction: column;
  align-items: center;
}
@media (min-width: 1024px) {
  .ocom-shop .k-profile-header {
    flex-direction: row;
  }
}

.ocom-shop .k-profile-header__button {
  margin-top: 1rem;
  font-size: 16px;
  padding: 2rem 3rem;
}
@media (min-width: 1024px) {
  .ocom-shop .k-profile-header__button {
    font-size: 20px;
  }
}

@media (min-width: 1024px) {
  .ocom-shop .k-profile-header__logo-wrapper {
    width: 200px;
    height: 150px;
    flex: 0 0 200px;
  }
}

@media (min-width: 1024px) {
  .ocom-shop .k-profile-header--sm-center {
    align-items: flex-start;
  }
}

.ocom-shop .k-offers__title {
  font-size: 14px;
  text-align: center;
}

.ocom-shop .k-offers__title a {
  text-decoration: none;
}

.ocom-shop .k-offers__title a:hover {
  text-decoration: underline;
}

.ocom-shop .k-offers__price {
  margin: auto;
  text-align: center;
}

@media (min-width: 1024px) {
  .ocom-shop__container {
    padding-left: 5%;
  }
}

.ocom-star svg {
  width: 26px;
  height: 26px;
  color: rgb(255, 208, 85);
}

.ocom-shop-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  padding: 1rem 0;
  margin-top: 1rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid #D9D9D9;
}
@media (min-width: 768px) {
  .ocom-shop-nav {
    padding: 30px 0;
    margin-top: 3rem;
    margin-bottom: 3rem;
  }
}

.ocom-shop-nav a {
  flex: 0 0 auto;
  font-size: 14px;
  color: #2b2930;
  padding: 1rem 2rem;
  font-weight: 700;
}
@media (min-width: 768px) {
  .ocom-shop-nav a {
    font-size: 16px;
    padding: 0 2rem;
  }
}

.ocom-shop-nav .hover-line {
  width: 130%;
  display: block;
  left: -15%;
  top: 31px;
  height: 1px;
  background-color: transparent;
  position: relative;
}

.ocom-shop-nav a:hover .hover-line {
  background-color: #2bb673;
}

.ocom-coupon .k-coupon__box {
  height: 100%;
}

.ocom-coupon .k-coupon__box-value {
  height: 100%;
  color: #fff;
  background-color: #2bb673;
  margin-bottom: 0;
}

.ocom-coupon .k-coupon__box-logo {
  justify-content: flex-start;
}

.ocom-coupon .k-coupon__box-logo img {
  width: auto;
  max-width: 80px;
  max-height: 60px;
}

.sd-content {
  position: relative;
  max-height: 100px;
  overflow: hidden;
  margin-bottom: 2rem;
  transition: max-height 0.2s ease-out;
}

.sd-content__show {
  position: absolute;
  display: flex;
  width: 100%;
  height: 76px;
  left: 0;
  bottom: 0;
  align-items: flex-end;
  justify-content: flex-start;
  background: linear-gradient(rgba(255, 255, 255, 0.1) 0%, #fff 60%);
  cursor: pointer;
}
@media (min-width: 768px) {
  .sd-content__show {
    font-size: 16px;
  }
}

.sd-content__show-inner {
  display: flex;
  align-items: center;
}

@media (min-width: 1180px) {
  .sd-content__show-inner:hover {
    text-decoration: underline;
  }
}

.sd-content__show-icon {
  margin-left: 8px;
}

.sd-content__content-btn {
  display: none;
}

.sd-content__content-btn:checked ~ .sd-content {
  max-height: 600px;
}

.sd-content__content-btn:checked ~ .sd-content .sd-content__show {
  display: none;
}

.product-content {
  position: relative;
  max-height: 380px;
  overflow: hidden;
  margin-bottom: 2rem;
  transition: max-height 0.2s ease-out;
}
@media (min-width: 768px) {
  .product-content {
    margin-bottom: 4rem;
  }
}

.product-content__show {
  position: absolute;
  display: flex;
  width: 100%;
  height: 76px;
  left: 0;
  bottom: 0;
  align-items: flex-end;
  justify-content: center;
  background: linear-gradient(rgba(255, 255, 255, 0.1) 0%, #fff 60%);
  cursor: pointer;
}
@media (min-width: 768px) {
  .product-content__show {
    font-size: 16px;
  }
}

.product-content__show-inner {
  display: flex;
  align-items: center;
}

@media (min-width: 1180px) {
  .product-content__show-inner:hover {
    text-decoration: underline;
  }
}

.product-content__show-icon {
  margin-left: 8px;
}

.product-content__content-btn {
  display: none;
}

.product-content__content-btn:checked ~ .product-content {
  max-height: 1200px;
}

.product-content__content-btn:checked ~ .product-content .product-content__show {
  display: none;
}

@media (min-width: 768px) {
  .k-shop__image-wrapper img {
    max-width: 120px;
  }
}

.product-tag__wrapper {
  position: absolute;
  display: flex;
  top: 1rem;
  right: 0;
  flex-direction: column;
  align-items: flex-end;
}

.product-tag__item {
  display: inline-flex;
  padding: 0.5rem;
  font-weight: 700;
  background-color: grey;
  color: #fff;
  font-size: 12px;
  margin-bottom: 0.25rem;
}

.success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.danger {
  background-color: #f8d7da;
  background-color: #f44336;
  border-color: #f5c6cb;
}

.warning {
  color: #856404;
  background-color: #fff3cd;
  border-color: #ffeeba;
}

.primary {
  color: #004085;
  background-color: #cce5ff;
  border-color: #b8daff;
}