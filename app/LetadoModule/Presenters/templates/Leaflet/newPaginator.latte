{var $countOfPages = count($leaflet->getPages())}

<div class="k-paginator__wrapper">
    <a n:if="$currentPage > 1" n:href="leaflet shop => $shop, leaflet => $leaflet, page => $currentPage-1" class="k-paginator__button">« {_'app.paginator.prev'}</a>
    <a n:if="$currentPage < $countOfPages" n:href="leaflet shop => $shop, leaflet => $leaflet, page => $currentPage+1" class="k-paginator__button">{_'app.paginator.next'} »</a>
</div>